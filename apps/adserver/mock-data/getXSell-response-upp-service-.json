{"service": "xSellServiceUppCart", "status": "success", "response": [{"adDetails": {"productSku": "CODEGUARD_BASIC_V2", "productMarkup": {"headContent": "        <title>codeguard_product</title>                            <link rel=\"canonical\" href=\"/content/experience-fragments/hostgator/promotion/codeguard/codeguard_product/master\">                                            ", "bodyContent": "                                                            <div class=\"responsivegrid\"><div class=\"aem-Grid aem-Grid--12 aem-Grid--default--12 \">        <div class=\"adsCard aem-GridColumn aem-GridColumn--default--12\"><style>    #codeguard.offers__container {        background-color: #F0FAFF;        border-radius: 10px;        border-width: 1px;        border-style: solid;        border-color: #D0D5E0;        padding: 16px;        position: relative;    }    .offers__container p {        margin: 0;    }    .offers__header {        display: flex;        align-items: center;    }    .offers__img__container img {        min-width: 44px;    }    .offers__title {        flex: 1 1 100%;    }    .offers__title p {        font-family: montserrat, Helvetica, sans-serif;        font-size: 18px;        font-weight: 700;        line-height: normal;    }    .offers__pricing {        text-align: right;        flex: 1 2 100%;        color: var(--mdc-theme-primary,#196CDF);    }    .offers__pricing .productPricing {        font-family: montserrat, Helvetica, sans-serif;        font-size: 16px;        font-weight: 700;    }    .offers__priceInfo {        font-family: Open Sans, sans-serif;        font-size: 12px;        font-weight: 400;    }    .offers__description {        border-bottom: 1px solid #ccc;        margin-bottom: 12px;        margin-top: 10px;        padding-bottom: 12px;    }    .offers__description p {        font-family: montserrat, Helvetica, sans-serif;    }    .offers__footer {        display: flex;    }    .offers__accordion {        flex: 1 1 100%;    }    .offers__accordion__btn {        color: var(--mdc-theme-primary,#196CDF);        background: none;        border: 0;        font-family: Open Sans, sans-serif;        font-size: 14px;        display: flex;        align-items: center;        padding: 0;        cursor: pointer;    }    .offers__accordion__btn svg {        fill: var(--mdc-theme-primary,#196CDF);        margin-left: 5px;    }    .offers__accordion__btn svg.d-none {        display: none;    }    .offers__cta {        margin-left: auto;    }    .addToCartBtn {        background-color: #30BAFF;        border-radius: ;        border-width: 1px;        border-style: solid;        cursor: pointer;        border-color: #6EB3FC;        color: #FFF;        font-family: Open Sans, sans-serif;        font-size: 14px;        font-weight: 700;        letter-spacing: 0.75px;        padding: 10px 40px;    }    .offers__accordion__container {        display: grid;        grid-template-rows: 0;    }    .offers__accordion__container.active--js {        grid-template-rows: 1fr;    }    .offers__accordion__content {        overflow: hidden;    }</style><div id=\"codeguard\" class=\"offers__container\">    <div class=\"offers__header\">        <div class=\"offers__img__container\" style=\"margin-right: 15px;\">            <img src=\"/content/dam/pega-treatments/incart-assets-bh/InCartCodeGuard.png\" alt=\"codeguard\" class=\"offers__img\" width=\"44\" height=\"44\">        </div>        <div class=\"offers__title\">            <p style=\"max-width: 85.0%;\">Protect your website with cloud-based backups.</p>        </div>        <div class=\"offers__pricing\" data-sf-requestdata=\"{&quot;productCode&quot;:&quot;CODEGUARD_BASIC_V2&quot;,&quot;livePricing&quot;:false,&quot;integerRoundDownForAttr&quot;:false,&quot;defaultPrice&quot;:false,&quot;subscriptionTerm&quot;:&quot;1&quot;,&quot;subscriptionUnit&quot;:&quot;1&quot;,&quot;populateDiscountMessage&quot;:false,&quot;skipSessionCoupon&quot;:false,&quot;hasParentPackage&quot;:false,&quot;integerRoundUpForAttr&quot;:false}\">            <p class=\"productPricing\">$3.99/mo</p>            <p class=\"offers__priceInfo\">Billed Annually</p>        </div>    </div>    <div class=\"offers__description\">        <p style=\"max-width: 85.0%;line-height: 26.0px;\">CodeGuard makes sure your site is automatically monitored for you. Get updates on any site changes and restore content in a click.&nbsp;</p>    </div>    <div class=\"offers__footer\">                <div class=\"offers__cta\">            <button type=\"button\" class=\"addToCartBtn\" data-element-type=\"AM Ads\" data-outcome=\"accept\" data-description=\"PM | InCart | Codeguard\" adtrackingbannerid=\"P180C112S1N0B200A20D277E0000V100\">ADD</button>        </div>    </div>    <div class=\"offers__accordion__container\">            </div></div></div>    </div></div>                                                                ", "htmlString": "<!DOCTYPE HTML><html lang=\"en-US\">    <head>    <meta charset=\"UTF-8\"/>    <title>codeguard_product</title>            <meta name=\"template\" content=\"content-only-template\"/>    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"/>            <link rel=\"canonical\" href=\"/content/experience-fragments/hostgator/promotion/codeguard/codeguard_product/master\"/>                                            </head>    <body class=\"content-only page basicpage\" id=\"content-only-8ff7e043d7\">                                                            <div class=\"responsivegrid\"><div class=\"aem-Grid aem-Grid--12 aem-Grid--default--12 \">        <div class=\"adsCard aem-GridColumn aem-GridColumn--default--12\"><style>    #codeguard.offers__container {        background-color: #F0FAFF;        border-radius: 10px;        border-width: 1px;        border-style: solid;        border-color: #D0D5E0;        padding: 16px;        position: relative;    }    .offers__container p {        margin: 0;    }    .offers__header {        display: flex;        align-items: center;    }    .offers__img__container img {        min-width: 44px;    }    .offers__title {        flex: 1 1 100%;    }    .offers__title p {        font-family: montserrat, Helvetica, sans-serif;        font-size: 18px;        font-weight: 700;        line-height: normal;    }    .offers__pricing {        text-align: right;        flex: 1 2 100%;        color: var(--mdc-theme-primary,#196CDF);    }    .offers__pricing .productPricing {        font-family: montserrat, Helvetica, sans-serif;        font-size: 16px;        font-weight: 700;    }    .offers__priceInfo {        font-family: Open Sans, sans-serif;        font-size: 12px;        font-weight: 400;    }    .offers__description {        border-bottom: 1px solid #ccc;        margin-bottom: 12px;        margin-top: 10px;        padding-bottom: 12px;    }    .offers__description p {        font-family: montserrat, Helvetica, sans-serif;    }    .offers__footer {        display: flex;    }    .offers__accordion {        flex: 1 1 100%;    }    .offers__accordion__btn {        color: var(--mdc-theme-primary,#196CDF);        background: none;        border: 0;        font-family: Open Sans, sans-serif;        font-size: 14px;        display: flex;        align-items: center;        padding: 0;        cursor: pointer;    }    .offers__accordion__btn svg {        fill: var(--mdc-theme-primary,#196CDF);        margin-left: 5px;    }    .offers__accordion__btn svg.d-none {        display: none;    }    .offers__cta {        margin-left: auto;    }    .addToCartBtn {        background-color: #30BAFF;        border-radius: ;        border-width: 1px;        border-style: solid;        cursor: pointer;        border-color: #6EB3FC;        color: #FFF;        font-family: Open Sans, sans-serif;        font-size: 14px;        font-weight: 700;        letter-spacing: 0.75px;        padding: 10px 40px;    }    .offers__accordion__container {        display: grid;        grid-template-rows: 0;    }    .offers__accordion__container.active--js {        grid-template-rows: 1fr;    }    .offers__accordion__content {        overflow: hidden;    }</style><div id=\"codeguard\" class=\"offers__container\">    <div class=\"offers__header\">        <div class=\"offers__img__container\" style=\"margin-right: 15px;\">            <img src=\"/content/dam/pega-treatments/incart-assets-bh/InCartCodeGuard.png\" alt=\"codeguard\" class=\"offers__img\" width=\"44\" height=\"44\"/>        </div>        <div class=\"offers__title\">            <p style=\"max-width: 85.0%;\">Protect your website with cloud-based backups.</p>        </div>        <div class=\"offers__pricing\" data-sf-requestData=\"{&#34;productCode&#34;:&#34;CODEGUARD_BASIC_V2&#34;,&#34;livePricing&#34;:false,&#34;integerRoundDownForAttr&#34;:false,&#34;defaultPrice&#34;:false,&#34;subscriptionTerm&#34;:&#34;1&#34;,&#34;subscriptionUnit&#34;:&#34;1&#34;,&#34;populateDiscountMessage&#34;:false,&#34;skipSessionCoupon&#34;:false,&#34;hasParentPackage&#34;:false,&#34;integerRoundUpForAttr&#34;:false}\">            <p class=\"productPricing\">$3.99/mo</p>            <p class=\"offers__priceInfo\">Billed Annually</p>        </div>    </div>    <div class=\"offers__description\">        <p style=\"max-width: 85.0%;line-height: 26.0px;\">CodeGuard makes sure your site is automatically monitored for you. Get updates on any site changes and restore content in a click. </p>    </div>    <div class=\"offers__footer\">                <div class=\"offers__cta\">            <button type=\"button\" class=\"addToCartBtn\" data-element-type=\"AM Ads\" data-outcome=\"accept\" data-description=\"PM | InCart | Codeguard\" adtrackingbannerid=\"P180C112S1N0B200A20D277E0000V100\">ADD</button>        </div>    </div>    <div class=\"offers__accordion__container\">            </div></div></div>    </div></div>                                                                </body></html>", "linkTags": [{"rel": "canonical", "href": "/content/experience-fragments/hostgator/promotion/codeguard/codeguard_product/master"}], "scriptTags": []}, "priority": 1, "actionType": "addToCart", "pricingTerm": "YEAR", "landingCode": "P180C112S1N0B200A20D277E0000V100", "channelID": "110", "coupon": "50offCodeguard"}}, {"adDetails": {"productSku": "SITELOCK_ESSENTIALS", "productMarkup": {"headContent": "        <title>sitelock_product</title>                            <link rel=\"canonical\" href=\"/content/experience-fragments/hostgator/promotion/sitelock/sitelock_product/master\">                                            ", "bodyContent": "                                                            <div class=\"responsivegrid\"><div class=\"aem-Grid aem-Grid--12 aem-Grid--default--12 \">        <div class=\"adsCard aem-GridColumn aem-GridColumn--default--12\"><style>    #sitelock.offers__container {        background-color: #F0FAFF;        border-radius: 10px;        border-width: 1px;        border-style: solid;        border-color: #D0D5E0;        padding: 16px;        position: relative;    }    .offers__container p {        margin: 0;    }    .offers__header {        display: flex;        align-items: center;    }    .offers__img__container img {        min-width: 44px;    }    .offers__title {        flex: 1 1 100%;    }    .offers__title p {        font-family: montserrat, Helvetica, sans-serif;        font-size: 18px;        font-weight: 700;        line-height: normal;    }    .offers__pricing {        text-align: right;        flex: 1 2 100%;        color: var(--mdc-theme-primary,#196CDF);    }    .offers__pricing .productPricing {        font-family: montserrat, Helvetica, sans-serif;        font-size: 16px;        font-weight: 700;    }    .offers__priceInfo {        font-family: Open Sans, sans-serif;        font-size: 12px;        font-weight: 400;    }    .offers__description {        border-bottom: 1px solid #ccc;        margin-bottom: 12px;        margin-top: 10px;        padding-bottom: 12px;    }    .offers__description p {        font-family: montserrat, Helvetica, sans-serif;    }    .offers__footer {        display: flex;    }    .offers__accordion {        flex: 1 1 100%;    }    .offers__accordion__btn {        color: var(--mdc-theme-primary,#196CDF);        background: none;        border: 0;        font-family: Open Sans, sans-serif;        font-size: 14px;        display: flex;        align-items: center;        padding: 0;        cursor: pointer;    }    .offers__accordion__btn svg {        fill: var(--mdc-theme-primary,#196CDF);        margin-left: 5px;    }    .offers__accordion__btn svg.d-none {        display: none;    }    .offers__cta {        margin-left: auto;    }    .addToCartBtn {        background-color: #30BAFF;        border-radius: ;        border-width: 1px;        border-style: solid;        cursor: pointer;        border-color: #6EB3FC;        color: #FFF;        font-family: Open Sans, sans-serif;        font-size: 14px;        font-weight: 700;        letter-spacing: 0.75px;        padding: 10px 40px;    }    .offers__accordion__container {        display: grid;        grid-template-rows: 0;    }    .offers__accordion__container.active--js {        grid-template-rows: 1fr;    }    .offers__accordion__content {        overflow: hidden;    }</style><div id=\"sitelock\" class=\"offers__container\">    <div class=\"offers__header\">        <div class=\"offers__img__container\" style=\"margin-right: 15px;\">            <img src=\"/content/dam/pega-treatments/incart-assets-bh/InCartSiteLock.png\" alt=\"sitelock\" class=\"offers__img\">        </div>        <div class=\"offers__title\">            <p>Cyberattacks have met their match.</p>        </div>        <div class=\"offers__pricing\" data-sf-requestdata=\"{&quot;productCode&quot;:&quot;SITELOCK_ESSENTIALS&quot;,&quot;livePricing&quot;:false,&quot;integerRoundDownForAttr&quot;:false,&quot;defaultPrice&quot;:false,&quot;subscriptionTerm&quot;:&quot;1&quot;,&quot;subscriptionUnit&quot;:&quot;2&quot;,&quot;populateDiscountMessage&quot;:false,&quot;skipSessionCoupon&quot;:false,&quot;hasParentPackage&quot;:false,&quot;integerRoundUpForAttr&quot;:false}\">            <p class=\"productPricing\">$7.99/mo</p>            <p class=\"offers__priceInfo\">Billed Annually</p>        </div>    </div>    <div class=\"offers__description\">        <p style=\"max-width: 85.0%;line-height: 26.0px;\">Scan your site daily without lifting a finger. SiteLock Essentials checks for malware, removes it, and provides the safety seal trusted by visitors and search engines alike.&nbsp;</p>    </div>    <div class=\"offers__footer\">                <div class=\"offers__cta\">            <button type=\"button\" class=\"addToCartBtn\" data-element-type=\"AM Ads\" data-outcome=\"accept\" data-description=\"PM | InCart | Sitelock\" adtrackingbannerid=\"P171C112S1N0B200A20D277E0000V100\">ADD</button>        </div>    </div>    <div class=\"offers__accordion__container\">            </div></div></div>    </div></div>                                                                ", "htmlString": "<!DOCTYPE HTML><html lang=\"en-US\">    <head>    <meta charset=\"UTF-8\"/>    <title>sitelock_product</title>            <meta name=\"template\" content=\"content-only-template\"/>    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"/>            <link rel=\"canonical\" href=\"/content/experience-fragments/hostgator/promotion/sitelock/sitelock_product/master\"/>                                            </head>    <body class=\"content-only page basicpage\" id=\"content-only-61689f03bc\">                                                            <div class=\"responsivegrid\"><div class=\"aem-Grid aem-Grid--12 aem-Grid--default--12 \">        <div class=\"adsCard aem-GridColumn aem-GridColumn--default--12\"><style>    #sitelock.offers__container {        background-color: #F0FAFF;        border-radius: 10px;        border-width: 1px;        border-style: solid;        border-color: #D0D5E0;        padding: 16px;        position: relative;    }    .offers__container p {        margin: 0;    }    .offers__header {        display: flex;        align-items: center;    }    .offers__img__container img {        min-width: 44px;    }    .offers__title {        flex: 1 1 100%;    }    .offers__title p {        font-family: montserrat, Helvetica, sans-serif;        font-size: 18px;        font-weight: 700;        line-height: normal;    }    .offers__pricing {        text-align: right;        flex: 1 2 100%;        color: var(--mdc-theme-primary,#196CDF);    }    .offers__pricing .productPricing {        font-family: montserrat, Helvetica, sans-serif;        font-size: 16px;        font-weight: 700;    }    .offers__priceInfo {        font-family: Open Sans, sans-serif;        font-size: 12px;        font-weight: 400;    }    .offers__description {        border-bottom: 1px solid #ccc;        margin-bottom: 12px;        margin-top: 10px;        padding-bottom: 12px;    }    .offers__description p {        font-family: montserrat, Helvetica, sans-serif;    }    .offers__footer {        display: flex;    }    .offers__accordion {        flex: 1 1 100%;    }    .offers__accordion__btn {        color: var(--mdc-theme-primary,#196CDF);        background: none;        border: 0;        font-family: Open Sans, sans-serif;        font-size: 14px;        display: flex;        align-items: center;        padding: 0;        cursor: pointer;    }    .offers__accordion__btn svg {        fill: var(--mdc-theme-primary,#196CDF);        margin-left: 5px;    }    .offers__accordion__btn svg.d-none {        display: none;    }    .offers__cta {        margin-left: auto;    }    .addToCartBtn {        background-color: #30BAFF;        border-radius: ;        border-width: 1px;        border-style: solid;        cursor: pointer;        border-color: #6EB3FC;        color: #FFF;        font-family: Open Sans, sans-serif;        font-size: 14px;        font-weight: 700;        letter-spacing: 0.75px;        padding: 10px 40px;    }    .offers__accordion__container {        display: grid;        grid-template-rows: 0;    }    .offers__accordion__container.active--js {        grid-template-rows: 1fr;    }    .offers__accordion__content {        overflow: hidden;    }</style><div id=\"sitelock\" class=\"offers__container\">    <div class=\"offers__header\">        <div class=\"offers__img__container\" style=\"margin-right: 15px;\">            <img src=\"/content/dam/pega-treatments/incart-assets-bh/InCartSiteLock.png\" alt=\"sitelock\" class=\"offers__img\"/>        </div>        <div class=\"offers__title\">            <p>Cyberattacks have met their match.</p>        </div>        <div class=\"offers__pricing\" data-sf-requestData=\"{&#34;productCode&#34;:&#34;SITELOCK_ESSENTIALS&#34;,&#34;livePricing&#34;:false,&#34;integerRoundDownForAttr&#34;:false,&#34;defaultPrice&#34;:false,&#34;subscriptionTerm&#34;:&#34;1&#34;,&#34;subscriptionUnit&#34;:&#34;2&#34;,&#34;populateDiscountMessage&#34;:false,&#34;skipSessionCoupon&#34;:false,&#34;hasParentPackage&#34;:false,&#34;integerRoundUpForAttr&#34;:false}\">            <p class=\"productPricing\">$7.99/mo</p>            <p class=\"offers__priceInfo\">Billed Annually</p>        </div>    </div>    <div class=\"offers__description\">        <p style=\"max-width: 85.0%;line-height: 26.0px;\">Scan your site daily without lifting a finger. SiteLock Essentials checks for malware, removes it, and provides the safety seal trusted by visitors and search engines alike. </p>    </div>    <div class=\"offers__footer\">                <div class=\"offers__cta\">            <button type=\"button\" class=\"addToCartBtn\" data-element-type=\"AM Ads\" data-outcome=\"accept\" data-description=\"PM | InCart | Sitelock\" adtrackingbannerid=\"P171C112S1N0B200A20D277E0000V100\">ADD</button>        </div>    </div>    <div class=\"offers__accordion__container\">            </div></div></div>    </div></div>                                                                </body></html>", "linkTags": [{"rel": "canonical", "href": "/content/experience-fragments/hostgator/promotion/sitelock/sitelock_product/master"}], "scriptTags": []}, "priority": 2, "actionType": "addToCart", "pricingTerm": "YEAR", "landingCode": "P171C112S1N0B200A20D277E0000V100", "channelID": "110", "coupon": "50offSitelock"}}], "trace": "a1e195195fc61af019f60ee6c5e5555ff07b3c16a7a636c9c25665e1250dea3030ec20518727a278710aae5d8282b215dde97843393532670aad38b8da4ee69b"}