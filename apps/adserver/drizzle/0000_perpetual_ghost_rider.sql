CREATE TABLE "decisions" (
	"decision_id" serial PRIMARY KEY NOT NULL,
	"brand" varchar(32) NOT NULL,
	"rule_id" integer,
	"decision_date" timestamp with time zone DEFAULT now() NOT NULL,
	"customer_id" bigint,
	"account_id" bigint,
	"surface" varchar(255) NOT NULL,
	"ad_identifier" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "rules" (
	"id" serial PRIMARY KEY NOT NULL,
	"brand" varchar(255) NOT NULL,
	"url" varchar(255) NOT NULL,
	"container_name" varchar(255) NOT NULL,
	"action_type" varchar(255) NOT NULL,
	"cart_includes_all" text[] DEFAULT '{}'::text[] NOT NULL,
	"cart_should_not_include" text[] DEFAULT '{}'::text[] NOT NULL,
	"pricing_term" varchar(255) NOT NULL,
	"price_display_term" varchar(255) NOT NULL,
	"landing_code" varchar(255) NOT NULL,
	"channel_id" varchar(255) NOT NULL,
	"coupon" text,
	"priority" integer,
	"fragment_ad_sku" text,
	"fragment_html_path" text,
	"fragment_html_path_qa" text
);
--> statement-breakpoint
ALTER TABLE "decisions" ADD CONSTRAINT "decisions_rule_id_rules_id_fk" FOREIGN KEY ("rule_id") REFERENCES "public"."rules"("id") ON DELETE no action ON UPDATE no action;