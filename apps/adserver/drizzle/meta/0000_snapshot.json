{"id": "5698d601-6680-4ac4-814b-6b67a2bee5a3", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.decisions": {"name": "decisions", "schema": "", "columns": {"decision_id": {"name": "decision_id", "type": "serial", "primaryKey": true, "notNull": true}, "brand": {"name": "brand", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "rule_id": {"name": "rule_id", "type": "integer", "primaryKey": false, "notNull": false}, "decision_date": {"name": "decision_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "customer_id": {"name": "customer_id", "type": "bigint", "primaryKey": false, "notNull": false}, "account_id": {"name": "account_id", "type": "bigint", "primaryKey": false, "notNull": false}, "surface": {"name": "surface", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ad_identifier": {"name": "ad_identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"decisions_rule_id_rules_id_fk": {"name": "decisions_rule_id_rules_id_fk", "tableFrom": "decisions", "tableTo": "rules", "columnsFrom": ["rule_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rules": {"name": "rules", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "brand": {"name": "brand", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "container_name": {"name": "container_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "action_type": {"name": "action_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "cart_includes_all": {"name": "cart_includes_all", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "cart_should_not_include": {"name": "cart_should_not_include", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "pricing_term": {"name": "pricing_term", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "price_display_term": {"name": "price_display_term", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "landing_code": {"name": "landing_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "coupon": {"name": "coupon", "type": "text", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false}, "fragment_ad_sku": {"name": "fragment_ad_sku", "type": "text", "primaryKey": false, "notNull": false}, "fragment_html_path": {"name": "fragment_html_path", "type": "text", "primaryKey": false, "notNull": false}, "fragment_html_path_qa": {"name": "fragment_html_path_qa", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}