import { iCacheItem } from 'app/models';
import { iRule } from '../models/aemRules';
import * as cheerio from 'cheerio';

/**
 * UPP wants us to separate the head and body from the HTML
 * which makes sense coz the returned HTML contains a <html>
 * and other stuff we cannot embed into the page
 * @param html
 * @returns
 */
export function parseHtml(html: string): iCacheItem {
  const $ = cheerio.load(html);

  $('head meta').remove(); // Remove meta tags from the head, we dont need them here

  const headContent = $('head').html()!;
  const bodyContent = $('body').html()!;

  const linkTags: Array<Record<string, string>> = [];
  $('head link')
    .each((_, element) => {
      const attributes: Record<string, string> = {};
      Object.entries((element as any).attribs).forEach(([key, value]) => {
        (attributes as any)[key] = value;
      });
      linkTags.push(attributes);
    })
    .remove();

  const scriptTags: Array<Record<string, string>> = [];
  $('head script')
    .each((_, element) => {
      const attributes: Record<string, string> = {};
      Object.entries((element as any).attribs).forEach(([key, value]) => {
        (attributes as any)[key] = value;
      });
      scriptTags.push(attributes);
    })
    .remove();

  $('body script').each((_, element) => {
    const attributes: Record<string, string> = {};
    Object.entries((element as any).attribs).forEach(([key, value]) => {
      (attributes as any)[key] = value;
    });
    scriptTags.push(attributes);
  });

  return {
    headContent,
    bodyContent,
    htmlString: html,
    linkTags,
    scriptTags,
  };
}

export const transformAd = (rule: iRule, bodyContent: string) => {
  const $ = cheerio.load(bodyContent);

  const defaultSelected = rule.fragmentDetails?.defaultSelectedPlan?.toLowerCase();
  if (defaultSelected) {
    const add__cta = $('.pricing__card .cta__link');

    const sku = defaultSelected.toLowerCase();
    const currentActive = $('.pricing__card .plan__contant.active--js');
    const button = $(`[data-sku="${sku}"]`);

    const planToBeSelected = button.closest('.plan__contant');

    // Remove 'active--js' from current active if it's different
    if (currentActive.length && !currentActive.is(planToBeSelected)) {
      currentActive.removeClass('active--js');
    }

    const priceElement = planToBeSelected.find('.plan_price');
    const price = priceElement.text() || '';

    const priceText = $('.price__container p.price');
    priceText.text(price);

    planToBeSelected.addClass('active--js');

    add__cta.attr('data-selected-plan', button.attr('data-value') as string);
    add__cta.attr('data-selected-sku', button.attr('data-sku') as string);
  }

  // Get the modified HTML back
  const modifiedHtml = $.html();
  return modifiedHtml;
};
