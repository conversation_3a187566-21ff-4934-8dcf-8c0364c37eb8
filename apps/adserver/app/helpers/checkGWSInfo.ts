import { fgProductLite, GwsDomain, Product } from 'app/models';
import { tBrand } from 'app/models/aemRules';
import { isDomain, isGoogleWorkspace } from './productIdentifiers';
import { fetchDomainsEligibleForGWS } from 'app/fetchers/fetchDomainsEligibleForGWS';
import { EVENT_MESSAGES, EVENT_TYPES } from 'app/constants';
import { sfLogger } from 'app/logger';

const checkEligibleForGWS = async (
  userId: string,
  accountId: string,
  productsInCart: Product[],
  maximumDomainsToCheckGWS: number,
  env: string,
  brand: tBrand,
  allDomainsRetrieved: fgProductLite[],
  surface: string,
  rngTrace: string,
): Promise<GwsDomain[]> => {
  let domainsToCheck: fgProductLite[] = [];

  const alreadyEligibleDomains: GwsDomain[] = [];
  const domainsInCartAsFgProductLite: fgProductLite[] = [];

  const GWSProducts = productsInCart
    .filter((product) => isGoogleWorkspace(product.productCode))
    .map((prod) => prod.domainName);

  productsInCart.forEach((product) => {
    const isDomainProduct = isDomain(product.productType);
    if (GWSProducts.includes(product.domainName) || !isDomainProduct || !product.domainName?.trim())
      return;

    if (product.transactionType === 'acquisition') {
      // The domains with productType as "acquisition" are already eligible for GWS
      alreadyEligibleDomains.push({
        prodInstName: product.domainName,
        prodInstId: product.productInstanceId,
        accountId: product.accountId,
      });
    } else {
      // Adding the rest of the domains to the array to check for GWS
      domainsInCartAsFgProductLite.push({
        prodCode: product.productCode,
        prodInstName: product.domainName,
        prodInstId: product.productInstanceId,
        accountId: product.accountId,
        prodType: product.productType,
        financialCdId: 0,
        createdDate: '',
        autoRenewFlag: false,
        lifecycleCdId: 0,
      });
    }
  });

  if ((userId || accountId) && allDomainsRetrieved && allDomainsRetrieved.length) {
    // Removing the domains for which the GWS is already added in the cart
    allDomainsRetrieved = allDomainsRetrieved.filter(
      (domain) => !GWSProducts.includes(domain.prodInstName),
    );

    // We will ignore the domains in account if they are more than the maximumDomainsToCheckGWS limit from the rule.
    // if maximumDomainsToCheckGWS is not available, we will take all the domains in consideration
    if (
      allDomainsRetrieved &&
      (!maximumDomainsToCheckGWS || allDomainsRetrieved.length <= maximumDomainsToCheckGWS)
    ) {
      domainsToCheck = [...domainsInCartAsFgProductLite, ...allDomainsRetrieved];
    } else {
      domainsToCheck = [...domainsInCartAsFgProductLite];
    }
  } else {
    // If userId isn't available, we will check the domains from the cart only.
    domainsToCheck = [...domainsInCartAsFgProductLite];
  }

  sfLogger.info(
    {
      eventType: EVENT_TYPES.BeforeTruncate,
      message: EVENT_MESSAGES.DomainsForGwsEligibility,
      customAttributes: {
        userId,
        accountId,
        brand,
        totalDomainsInAccount: domainsToCheck.length,
      },
    },
    rngTrace,
  );

  // Not flooding the GWS eligibility API if we have too many domains to check. Limiting to 20.
  if (domainsToCheck.length > 20) {
    domainsToCheck.length = 20;
  }

  // Checking each rule whether it is eligible
  const eligibleProductsForGWS = await fetchDomainsEligibleForGWS(
    env,
    domainsToCheck,
    brand,
    userId,
    surface,
    rngTrace,
  );

  const gwsDomains: GwsDomain[] = [...alreadyEligibleDomains];

  gwsDomains.push(
    ...eligibleProductsForGWS
      .filter((domain: fgProductLite) => isDomain(domain.prodType))
      .map((product) => {
        return {
          prodInstName: product.prodInstName,
          prodInstId: product.prodInstId,
          accountId: product.accountId,
        };
      }),
  );

  return gwsDomains;
};

export { checkEligibleForGWS };
