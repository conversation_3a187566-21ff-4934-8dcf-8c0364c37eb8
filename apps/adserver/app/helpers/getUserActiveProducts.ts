import { ERROR_MESSAGES, EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';
import { sfLogger } from 'app/logger';
import { fgProductLite } from 'app/models';
import {
  getAccountsForUser,
  getProductsForSingleAccount,
} from 'app/services/userAccountQueryService';
import { isLifecycleActive } from './productIdentifiers';

type CustomerData = {
  status: string;
  errorCode: string | null;
  data: {
    transactionId: string;
    products: fgProductLite[];
    totalProductsCount: number;
  };
  errMessage: string | null;
};

const getUserActiveProducts = async (
  userId: string,
  env: string,
  brand: string,
  responseType: string,
  rngTrace: string,
): Promise<fgProductLite[]> => {
  let customerData: CustomerData | undefined;
  let activeProducts: fgProductLite[] = [];
  const accountsResponseData = await getAccountsForUser(env, userId);

  let {
    status: accountQueryStatus,
    errorCode: accountQueryErrorCode,
    errMessage: accountQueryErrMessage,
  } = accountsResponseData;

  if (accountQueryStatus === 'error') {
    sfLogger.error(
      {
        eventType: EVENT_TYPES.QueryAccounts_Fail,
        message: ERROR_MESSAGES.QueryAccountsFailed,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_10,
          userId,
          accountQueryErrMessage,
          accountQueryErrorCode,
        },
      },
      rngTrace,
    );
    throw new Error(
      `Problem querying the accounts for the userId ${userId}: ${accountQueryErrMessage}`,
    );
  }

  //query the products for each account, combine, and apply rules
  for (let i = 0; i < accountsResponseData.data.userAccounts?.length; i++) {
    const { accountId } = accountsResponseData.data.userAccounts[i];
    // query the products in the user acct
    let activeProductsForAccount: fgProductLite[] = [];

    try {
      customerData = await getProductsForSingleAccount(env, accountId);
      let {
        status: productQueryStatus,
        errorCode: productQueryErrorCode,
        errMessage: productQueryErrorMessage,
      } = customerData;

      if (productQueryStatus === 'error') {
        sfLogger.error(
          {
            eventType: EVENT_TYPES.QueryAccounts_Fail,
            message: ERROR_MESSAGES.QueryProductsFailed,
            customAttributes: {
              initiator: SFLOGGER_IDS.SFLOG_ID_11,
              accountId,
              productQueryErrorMessage,
              productQueryErrorCode,
            },
          },
          rngTrace,
        );
        throw new Error(
          `getActiveProducts() - problem querying the products for account ${accountId}: ${productQueryErrorMessage}`,
        );
      }

      if (customerData && productQueryStatus === 'success') {
        //locate active products by their lifecycleCdId
        for (let j = 0; j < customerData.data.products?.length; j++) {
          if (isLifecycleActive(customerData.data.products[j].lifecycleCdId)) {
            activeProductsForAccount.push(customerData.data.products[j]);
          }
        }

        if (activeProductsForAccount?.length > 0) {
          activeProducts = activeProducts.concat(activeProductsForAccount);
        }
      }
    } catch (err: any) {
      sfLogger.error(
        {
          eventType: EVENT_TYPES.QueryAccounts_Fail,
          message: ERROR_MESSAGES.QueryActiveProductsError,
          errorDetails: (err as Error).message,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_12,
            brand,
            accountId,
            responseType,
          },
        },
        rngTrace,
      );
      const errorPrefix =
        'Error returned to getActiveProducts during call to get products for account';
      if (err.message) {
        console.error(`${errorPrefix} ${accountId} : ${err.message}`);
      } else {
        console.error(`${errorPrefix} ${accountId} : ${JSON.stringify(err, null, '\t')}`);
      }
    }
  }

  return activeProducts;
};

/**
 * Retrieves a list of active products for a given account.
 *
 * @param accountId The ID of the account.
 * @param env The environment (e.g., 'dev', 'prod').
 * @param brand The brand of the products.
 * @param responseType The type of response expected.
 * @param rngTrace The trace ID for request tracking.
 * @returns A promise of fgProductLite[].  Returns an empty array if no active products.
 * @throws {Error} If there is a problem querying products for the account.
 */
const getProductsByAccountId = async (
  accountId: string,
  env: string,
  brand: string,
  responseType: string,
  rngTrace: string,
): Promise<fgProductLite[]> => {
  let customerData: CustomerData | undefined;
  let activeProducts: fgProductLite[] = [];

  try {
    customerData = await getProductsForSingleAccount(env, accountId);

    let {
      status: queryStatus,
      errorCode: queryErrorCode,
      errMessage: queryErrorMessage,
    } = customerData;

    if (queryStatus === 'error') {
      sfLogger.error(
        {
          eventType: EVENT_TYPES.QueryAccounts_Fail,
          message: ERROR_MESSAGES.QueryProductsFailed,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_40,
            accountId,
            productQueryErrorMessage: queryErrorMessage,
            productQueryErrorCode: queryErrorCode,
          },
        },
        rngTrace,
      );

      throw new Error(`Problem querying products for account ${accountId}: ${queryErrorMessage}`);
    }

    if (Array.isArray(customerData?.data?.products) && queryStatus === 'success') {
      for (let product of customerData.data.products) {
        if (isLifecycleActive(product.lifecycleCdId)) {
          activeProducts.push(product);
        }
      }
    }
  } catch (err: any) {
    sfLogger.error(
      {
        eventType: EVENT_TYPES.QueryAccounts_Fail,
        message: ERROR_MESSAGES.QueryActiveProductsError,
        errorDetails: (err as Error).message,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_41,
          brand,
          responseType,
          accountId,
        },
      },
      rngTrace,
    );

    const errorPrefix =
      'Error returned to getActiveProducts during call to get products for account';
    if (err.message) {
      console.error(`${errorPrefix} ${accountId} : ${err.message}`);
    } else {
      console.error(`${errorPrefix} ${accountId} : ${JSON.stringify(err, null, '\t')}`);
    }
  }

  return activeProducts;
};

export { getUserActiveProducts, getProductsByAccountId };
