/**
 * We dont need to return those ads if any of skus present in cartShouldNotInclude is present in the cart
 * This helper function filters on AEM rules and removes such rules if any of the skus in cartShouldNotInclude is found in cart'
 * @param aemRules
 * @param cartSkus
 * @returns filtered AEM rules
 */

import { iRule } from 'app/models/aemRules';

const rejectUnallowedProducts = (aemRules: iRule[], cartSkus: string[]) => {
  const filteredRules = aemRules?.filter(
    (rule) => !cartSkus?.some((item: string) => rule?.cartShouldNotInclude?.includes(item)),
  );

  return { rules: filteredRules };
};

export { rejectUnallowedProducts };
