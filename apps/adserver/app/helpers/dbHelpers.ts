import { and, eq, gt, SQL } from 'drizzle-orm';
import { readDecision } from '../../../../packages/common/src/repository';
import { decisionsTable } from '@repo/common';
import { FilteredDBRules, iRule, tContainerName } from 'app/models/aemRules';
import { sfLogger } from 'app/logger';
import { EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';

const filterTodaysUnservedRules = async (
  allRules: iRule[],
  currentContainerName: tContainerName,
  userId: string,
  brand: string,
  rngTrace: string,
) => {
  const today = new Date();
  const todayMidnightUTC = new Date(
    Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate()),
  );

  try {
    const whereCondition: SQL | undefined = and(
      eq(decisionsTable.brand, brand),
      eq(decisionsTable.customerId, Number(userId)),
      eq(decisionsTable.surface, currentContainerName),
      gt(decisionsTable.decisionDate, todayMidnightUTC),
    );

    const selectColumns = {
      adIdentifier: decisionsTable.adIdentifier,
    };

    const adsServedToday = (await readDecision(whereCondition, selectColumns)).map(
      (ad) => ad.adIdentifier,
    );

    let response: FilteredDBRules = {
      adsNotServedToday: allRules,
      adsServedToday: [],
    };
    if (adsServedToday.length > 0) {
      response.adsNotServedToday = allRules.filter(
        (rule) => !adsServedToday.includes(rule.businessRule),
      );
      response.adsServedToday = adsServedToday;
    }
    return response;
  } catch (error) {
    sfLogger.error(
      {
        eventType: EVENT_TYPES.DbRulesNotFound,
        message: "Error fetching today's unserved rules from database",
        errorDetails: (error as Error).message,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_33,
          brand,
          userId,
          surface: currentContainerName,
        },
      },
      rngTrace,
    );
    return { adsNotServedToday: allRules, adsServedToday: [] };
  }
};

export { filterTodaysUnservedRules };
