import { fetchJwtToken } from '../fetchers/jwtFetcher';
import { querySitesForHosting } from '../fetchers/hostingInfoFetcher';
import { sfLogger } from 'app/logger';
import { ERROR_MESSAGES, EVENT_MESSAGES, EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';
import { productEligibilityResult } from '../models/index';
import { Env } from 'app/models/aemRules';
import { PROD, STAGE } from 'app/constants';

/**
 * for the current ad being evaluated, check if the
 * sku is one which requires an eligibility check
 * @param productCode
 * @returns boolean
 */
const isEligibilityCheckRequired = (sku: string) => {
  if (sku === 'YOAST_SEO_PREMIUM') {
    return true;
  }
  return false;
};

/**
 * for the current ad sku being evaluated, check if the current
 * product in the account is one which matches the eligible productCodes
 * @param productCode
 * @returns boolean
 */
const isEligibleProductCodeForSku = (sku: string, productCode: string) => {
  let isEligible = false;
  switch (sku) {
    case 'YOAST_SEO_PREMIUM':
      if (
        productCode === 'BH_PKG_WP_ENT' ||
        productCode === 'BH_PKG_WP_PLUS' ||
        productCode === 'BH_PKG_WP_CHOICE_PLUS' ||
        productCode === 'BH_PKG_WP_PRO' ||
        productCode === 'BH_HP_PKG_STARTER' ||
        productCode === 'BH_HP_PKG_PLUS' ||
        productCode === 'BH_HP_PKG_BUSINESS' ||
        productCode === 'BH_HP_PKG_PRO' ||
        productCode === 'BH_PKG_WP_PRO_150' ||
        productCode === 'BH_PKG_WP_PRO_200' ||
        productCode === 'BH_PKG_WP_PRO_250' ||
        productCode === 'BH_ECOMM_STORE' ||
        productCode === 'BH_ECOMM_STORE_MKTPL' ||
        productCode === 'BH_CREATOR_SOLUTION_HOSTING' ||
        productCode === 'BH_COMMERCE_SOLUTION_HOSTING' ||
        productCode === 'BH_HP_PKG_PREMIUM' ||
        productCode === 'BH_HP_PKG_ENHANCED' ||
        productCode === 'BH_HP_PKG_ELITE' ||
        productCode === 'BH_SH_PKG_DESTINY_STARTER' ||
        productCode === 'BH_SH_PKG_DESTINY_PLUS' ||
        productCode === 'BH_SH_PKG_DESTINY_BUSINESS' ||
        productCode === 'BH_SH_PKG_DESTINY_PRO' ||
        productCode === 'BH_SH_PKG_DESTINY_PREMIUM' ||
        productCode === 'BH_SH_PKG_DESTINY_ENHANCED' ||
        productCode === 'BH_SH_PKG_DESTINY_ELITE' ||
        productCode === 'BH_SH_PKG_DESTINY_CREATOR' ||
        productCode === 'BH_SH_PKG_DESTINY_SOLUTION'
      ) {
        isEligible = true;
      }
      break;
    default:
  }
  return isEligible;
};

/**
 * For a user, check if any of the eligible products have eligible sites
 * @param configEnv
 * @param brand
 * @param userId
 * @param hostingForUser
 * @param sku
 * @param rngTrace
 * @returns boolean
 */
const doesUserHaveEligibleSites = async (
  configEnv: string,
  brand: string,
  userId: string,
  hostingForUser: any,
  sku: string,
  rngTrace: string,
): Promise<productEligibilityResult> => {
  let isEligibilityCheckPassed: boolean = false;
  let siteId;
  let matchingProdCode;
  let matchingAccountId;
  let statusMessage = "";

  if (hostingForUser && hostingForUser.length > 0) {
    for (let i = 0; i < hostingForUser.length; i++) {
      if (isEligibleProductCodeForSku(sku, hostingForUser[i].prodCode)) {
        /*
        let accountId;
        let accountIdStr;
        let step = '1';
        try {
          accountId = hostingForUser[i].accountId;
          accountIdStr = accountId.toString();
          let token: string = await fetchJwtToken(configEnv, brand, userId, accountIdStr);

          // check if the hosting has sites available.
          if (token !== '-1') {
            step = '2';
            let sitesResponse: any = await querySitesForHosting(
              configEnv,
              token,
              hostingForUser[i].prodInstId,
            );

            if (sitesResponse?.data?.total > 0) {
              step = '3';
              siteId = sitesResponse?.data?.items[0]?.id;
              if (siteId) {
                matchingProdCode = hostingForUser[i].prodCode;
                matchingAccountId = accountId;
                isEligibilityCheckPassed = true;
                statusMessage = 'product eligibility check succeeded';
                sfLogger.info(
                  {
                    eventType: EVENT_TYPES.CheckForEligibleSites,
                    message: EVENT_MESSAGES.siteEligibilityCheckPerformed,
                    customAttributes: {
                      initiator: SFLOGGER_IDS.SFLOG_ID_48,
                      brand,
                      siteId,
                      accountId: accountIdStr,
                      userId,
                      matchingProdCode,
                    },
                  },
                  rngTrace,
                );
              }
              break;
            } else {
              statusMessage = 'product eligibility check returned no eligible sites';
            }
          } else {
            console.log(
              'productEligibilityHelper.doesUserHaveEligibleSites() - token returned was = -1',
            );
            statusMessage = 'product eligibility check problem, token returned was = -1';
            sfLogger.error(
              {
                eventType: EVENT_TYPES.CheckForEligibleSites,
                message: 'problem when retrieving jwt token',
                errorDetails: 'token returned was = -1',
                customAttributes: {
                  initiator: SFLOGGER_IDS.SFLOG_ID_49,
                  brand,
                  userId,
                  accountId: accountIdStr,
                  sku,
                },
              },
              rngTrace,
            );
          }
        } catch (err) {
          const errMessage = (err as Error).message;
          if (configEnv === PROD) {
            statusMessage = 'product eligibilty check failed';
          } else {
            statusMessage = `product eligibilty check failed: ${errMessage}`;
          }
          console.log(
            `productEligibilityHelper.doesUserHaveEligibleSites() - error occurred: ${errMessage}`,
          );
          sfLogger.error(
            {
              eventType: EVENT_TYPES.CheckForEligibleSites,
              message: 'problem when performing check for eligible sites',
              errorDetails: errMessage,
              customAttributes: {
                initiator: SFLOGGER_IDS.SFLOG_ID_50,
                brand,
                userId,
                accountId: accountIdStr,
                sku,
                step,
              },
            },
            rngTrace,
          );
        }
        
        // TODO: we need to verify the connectivity is working in PROD
        // and STG envs, so i feel we should verify the connection works
        // before we actually depend on the result, so if there is an
        // exception we will just log it for now. Also will log success.
        //if (configEnv === PROD || configEnv === STAGE) {
        if (configEnv === STAGE) {
          isEligibilityCheckPassed = true;
        }
        */
        
        isEligibilityCheckPassed = true;
      }
    }
  }
  const productEligResult: productEligibilityResult = {
    isEligibilityCheckPassed,
    statusMessage,
  };
  return productEligResult;
};

export { isEligibilityCheckRequired, isEligibleProductCodeForSku, doesUserHaveEligibleSites };
