import { FREE, EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';
import { iPricingRule, ProductPrice } from 'app/models/aemRules';
import axios from 'axios';
import { sfLogger } from 'app/logger';
import { QA } from 'app/constants';
export type iPricingBffTerm = 'month' | 'year' | 'one time shipped';

/**
 * Helper function to get static pricing from the BFF layer
 * @param sku
 * @param brand
 * @param term
 * @returns
 */
export async function getProductPrice(
  sku: string | string[],
  brand: string,
  pricingTerm: iPricingBffTerm,
  pricingDisplayTerm: iPricingBffTerm,
  coupon: string,
  currencyCode: string,
  pricingSiteId: number | undefined,
  env: string,
  statusMessages: string[],
  containerName: string,
): Promise<ProductPrice> {
  let retVal: ProductPrice;

  let currentReq = `https://sfbff.newfold.com/getProductPrice/product/${sku}?bs=jarvis&brand=${brand}&currencyCode=${currencyCode} and couponCode=${coupon} and siteId=${pricingSiteId}`;
  try {
    if (!sku) {
      sfLogger.warn(
        {
          eventType: EVENT_TYPES.PriceInjection,
          message: `sku is missing in ad`,
          errorDetails: 'will skip price injection',
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_14,
            brand,
            sku,
            currencyCode,
            coupon,
            siteId: pricingSiteId,
          },
        },
        '',
      );
      return retVal;
    }

    const response = await axios.get(
      `https://sfbff.newfold.com/getProductPrice/product/${sku}?bs=JARVIS&brand=${brand}&currencyCode=${currencyCode}${
        coupon ? `&couponCode=${coupon}` : ''
      }${pricingSiteId ? `&siteId=${pricingSiteId}` : ''}${
        env === QA && brand !== 'HOSTGATOR' ? `&env=${env}` : ''
      }`, // env param is not appended for HOSTGATOR
    );
    const data = response.data;

    // if we need to do anything more with the BFF except get a single price, i dont think we need the types

    // Find the pricing rule object where the termUnitDescription matches the pricingTerm and the termQuantity is 1 (for now)

    const priceTermObj: iPricingRule = data.terms.find(
      (i: any) => i.termUnitDescription === pricingTerm && i.termQuantity === 1,
    );
    // Get the price from the pricing term object
    let price: string = priceTermObj?.[getPriceKey(pricingDisplayTerm)];
    let basePrice: string = priceTermObj?.monthlyBasePrice;
    if (!price) {
      sfLogger.warn(
        {
          eventType: EVENT_TYPES.PriceInjection,
          message: `sku has undefined price for ad`,
          errorDetails: 'matching pricingTerm not found',
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_15,
            brand,
            sku,
            currencyCode,
            coupon,
            pricingTerm,
            pricingDisplayTerm,
            price,
            siteId: pricingSiteId,
          },
        },
        '',
      );
    }

    const currencySymbol = data.currencySymbol;

    if (currencyCode !== 'USD' && currencySymbol === '$') {
      sfLogger.error(
        {
          eventType: EVENT_TYPES.ADAM_getProductPrice,
          message: `Received incorrect currencySymbol`,
          errorDetails: '',
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_16,
            brand,
            sku,
            currencyCode,
            coupon,
            currencySymbol,
            siteId: pricingSiteId,
            surface: containerName,
          },
        },
        '',
      );
    }

    // Checking Number(price) to cover "0", "0.00" etc cases
    if (Number(price) === 0) {
      retVal = { price: FREE, basePrice: currencySymbol + basePrice };
    } else {
      retVal = { price: currencySymbol + price, basePrice: currencySymbol + basePrice };
    }
  } catch (e: any) {
    const errorMessage =
      `error - pricingBffHelper - brand=${brand} - data sku ${sku}, currencySymbol ${currencyCode} - ` +
      e.message +
      `.... for request url = ${currentReq}`;
    console.error(errorMessage);
    statusMessages.push(errorMessage);
    retVal = undefined;
    sfLogger.error(
      {
        eventType: EVENT_TYPES.ADAM_getProductPrice,
        message: `Failed to fetch price for ad sku`,
        errorDetails: e.message,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_17,
          brand,
          sku,
          currencyCode,
          coupon,
          siteId: pricingSiteId,
          surface: containerName,
        },
      },
      '',
    );
  } finally {
    return retVal;
  }
}

export const getRawProductPrice = async (
  sku: string | string[],
  brand: string,
  coupon: string,
  currencyCode: string,
  pricingSiteId: number | undefined,
  env: string,
) => {
  const response = await axios.get(
    `https://sfbff.newfold.com/getProductPrice/product/${sku}?bs=jarvis&brand=${brand}&currencyCode=${currencyCode}${
      coupon ? `&couponCode=${coupon}` : ''
    }${pricingSiteId ? `&siteId=${pricingSiteId}` : ''}${env === QA ? `&env=${env}` : ''}`, //- add this if we want env to be part of the request
  );
  const data = response.data;
  return { terms: data.terms || [], currencySymbol: data.currencySymbol };
};

// Returns the key to get the price from the pricing term object
export const getPriceKey = (term: iPricingBffTerm, basePrice: boolean = false) => {
  switch (term) {
    case 'month':
      return basePrice ? 'monthlyBasePrice' : 'monthlyPrice';
    case 'one time shipped':
    case 'year':
    default:
      return basePrice ? 'basePrice' : 'finalPrice';
  }
};
