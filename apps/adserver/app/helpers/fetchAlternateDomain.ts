import { fgProductLite } from 'app/models';
import { Env, tAltDomainParams, tBrand } from 'app/models/aemRules';

const fetchAlternateDomain = async (
  brand: tBrand,
  env: Env,
  domains: fgProductLite,
): Promise<any> => {
  let retVal = [];
  const params: tAltDomainParams = {
    brand,
    domains: domains.prodInstName,
    env: env,
    useConfigTlds: true,
    spinSearch: true,
    aftermarketDomainsReq: false,
    registryPremium: false,
    includePremiumDomainsInTopTlds: false,
    spinDomainsWithoutTldsReq: false,
    aftermarket: false,
    client: 'UPP',
    currencyCode: 'USD',
    tldOrder: [
      '.com',
      '.net',
      '.org',
      '.store',
      '.online',
      '.site',
      '.tech',
      '.info',
      '.biz',
      '.us',
      '.ca',
    ],
  };

  try {
    let url = `https://sfbff-domain-search-storefrontbff.apps.atlanta1.newfoldmb.com/api/v1/domain-search`;

    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        const param = params[key as keyof tAltDomainParams];
        if (Array.isArray(param)) {
          // Looping over tldOrders
          param.forEach((item) => {
            url += (url.includes('?') ? '&' : '?') + `${key}=${item}`;
          });
        } else {
          url += (url.includes('?') ? '&' : '?') + `${key}=${param}`;
        }
      }
    }

    const response = await fetch(url);
    const res = await response.json();
    if (res?.response?.data?.spinDomains) {
      // Setting a maximum length of 3 for alternate domains
      res.response.data.spinDomains.length = Math.min(res.response.data.spinDomains.length, 3);
      retVal = res.response.data.spinDomains;
    } else if (res?.error) {
      throw new Error(res.error);
    }
    return retVal;
  } catch (err) {
    throw new Error(err as string);
  }
};

export default fetchAlternateDomain;
