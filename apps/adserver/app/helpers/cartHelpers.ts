import { Product } from 'app/models/index';
import { isDomain } from './productIdentifiers';

const findDomainCodesInCart = (cartItems: string[]) => {
  if (cartItems && cartItems.length) {
    return cartItems.filter((productType) => {
      let isMatch = isDomain(productType);
      return isMatch;
    });
  } else {
    return [];
  }
};

const findDomainNames = (products: Product[]) => {
  const domains: string[] = [];
  products.forEach((product: Product) => {
    if (product.productType === 'SFDomain' && product.domainName) {
      domains.push(product.domainName);
    }
  });
  return domains;
};

const findProductsInCart = (cartItems: string[], skus: string[]) => {
  if (cartItems && cartItems.length) {
    return cartItems.filter((productCode) => {
      let isMatch = false;
      skus.forEach((sku) => {
        if (productCode === sku) {
          isMatch = true;
        }
      });
      return isMatch;
    });
  } else {
    return [];
  }
};

const getAdCodesFromProduct = (productCodeInCart: string): string[] | undefined => {
  switch (productCodeInCart) {
    case 'BH_HP_PKG_STARTER':
      return ['SITELOCK_ESSENTIALS', 'TITAN_MAIL_TRIAL', 'CODEGUARD_BASIC_V2', 'YOAST_SEO_PREMIUM'];
    case 'BH_HP_PKG_BUSINESS':
      return ['CODEGUARD_BASIC_V2', 'TITAN_MAIL_TRIAL', 'WP_SOLUTION_CREATOR', 'YOAST_SEO_PREMIUM'];
    case 'BH_CREATOR_SOLUTION_HOSTING':
      return ['CODEGUARD_BASIC_V2', 'TITAN_MAIL_TRIAL', 'YOAST_SEO_PREMIUM'];
    case 'BH_HP_PKG_PRO':
      return ['CODEGUARD_BASIC_V2', 'TITAN_MAIL_TRIAL', 'WP_SOLUTION_CREATOR', 'YOAST_SEO_PREMIUM'];
    case 'BH_HP_PKG_PREMIUM':
      return ['CODEGUARD_BASIC_V2', 'TITAN_MAIL_TRIAL', 'WP_SOLUTION_CREATOR', 'YOAST_SEO_PREMIUM'];
    case 'BH_HP_PKG_ENHANCED':
      return ['CODEGUARD_BASIC_V2', 'TITAN_MAIL_TRIAL', 'WP_SOLUTION_CREATOR', 'YOAST_SEO_PREMIUM'];
    case 'BH_HP_PKG_ELITE':
      return ['CODEGUARD_BASIC_V2', 'TITAN_MAIL_TRIAL', 'WP_SOLUTION_CREATOR', 'YOAST_SEO_PREMIUM'];
  }
};

export { findDomainCodesInCart, findDomainNames, findProductsInCart, getAdCodesFromProduct };
