import {
  BRANDS,
  RESPONSE_TYPES,
  CONTAINER_NAME_TYPES,
  CHANNEL_TYPES,
  PROD,
  QA,
  STAGE,
  DEVELOPMENT,
  JARVISQA1,
} from 'app/constants';
import { XSellRequestBody } from 'app/models';

export const validateRequestBody = (body: XSellRequestBody): { valid: boolean; error?: string } => {
  const {
    brand,
    responseType,
    containerName,
    userId = undefined,
    channel = undefined,
    env,
    testOffers,
  } = body;

  if (testOffers?.length) return { valid: true };

  if (!BRANDS.includes(brand)) {
    return { valid: false, error: 'Invalid brand' };
  }

  if (responseType && !RESPONSE_TYPES.includes(responseType)) {
    return { valid: false, error: 'Invalid responseType. Examples: fragId or frag or rawFrag' };
  }

  if (!CONTAINER_NAME_TYPES.includes(containerName)) {
    return { valid: false, error: 'Invalid containerName. Examples: InCart or AMHPCards or AMWI' };
  }

  if (channel && !CHANNEL_TYPES.includes(channel)) {
    return { valid: false, error: 'Invalid channel. Examples: Web or Mobile or Tablet' };
  }

  if (userId && isNaN(Number(userId))) {
    return {
      valid: false,
      error: 'Invalid userId. For userId we require the numeric personOrgId, example 999676446',
    };
  }

  if (containerName === 'InCart' && (!body.cart || !body.cart?.cards)) {
    return { valid: false, error: 'Invalid cart. Cart should contain cards and products' };
  }

  if (
    !(
      (env as string) === PROD ||
      (env as string) === STAGE ||
      (env as string) === QA ||
      (env as string) === DEVELOPMENT ||
      (env as string) === JARVISQA1
    )
  ) {
    return { valid: false, error: 'Invalid env. Examples: qa, stg, prod, development' };
  }

  return { valid: true };
};
