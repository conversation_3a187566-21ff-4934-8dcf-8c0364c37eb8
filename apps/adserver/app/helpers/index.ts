import compression from 'compression';
import { EVENT_TYPES, WHITELIST, SFLOGGER_IDS, EVENT_MESSAGES } from '../constants';
import { Request, Response } from 'express';
import { sfLogger } from 'app/logger';
import { tBrand, Env, iRule } from 'app/models/aemRules';
import { AdObj, AdResponse } from 'app/models';
import { configEnv } from '../models/index';
import { PROD, QA } from 'app/constants';

const getEnv = (host: string) => {
  if (host.includes('localhost') || host.includes('127.0.0.1')) {
    return 'development';
  } else if (host.startsWith('sfbff-dev')) {
    return 'qa';
  } else if (host.includes('sfbff-prod')) {
    return 'production';
  }

  return 'development';
};

const isValidEnv = (env: Env): boolean => {
  let isValid = true;
  if (!env || (env != 'qa' && env != 'prod')) {
    isValid = false;
  }
  return isValid;
};

/* we only maintain prod and qa settings in the
   cache. So that is why we convert the env to 'qa'
   But for some envs we want to have separate config settings
   since we need to point to env specific urls, so we
   use the configEnv for keeping track of that.
*/
const getConfigEnv = (env: string): configEnv => {
  let configEnvResult: string = env;
  let envResult: string;

  if ((env as any) === PROD || (env as any) === QA) {
    envResult = env;
  } else {
    envResult = 'qa';
  }

  const configEnvInfo: configEnv = {
    configEnv: configEnvResult,
    env: envResult,
  };
  return configEnvInfo;
};

const getHost = (env: string) => {
  if (env === 'production') {
    return 'sfbff-prod.apps.atlanta1.newfoldmb.com/';
  } else {
    return 'sfbff-dev.apps.atlanta1.newfoldmb.com/';
  }
};

const getAllowListedDomains = () => {
  return '*.bluehost.com *.domain.com *.hostgator.com *.networksolutions.com *.register.com *.web.com, *.crazydomains.*';
};

const getCorsOptions = () => {
  const corsOptions = {
    origin: function (origin: any, callback: Function) {
      if (!origin) {
        callback(null, true);
        return;
      }

      try {
        const originHostnameWithPossiblePort = new URL(origin).hostname;
        const originHostname = originHostnameWithPossiblePort.split(':')[0];

        // AEM Authoring server support
        if (originHostname.endsWith('.web.com')) {
          console.log('Cors: ends with .web.com');
          callback(null, origin);
          return;
        }

        // Support all CrazyDomains CCtlds and AEM subdomain and AdServer Render App
        if (
          originHostname.startsWith('www.crazydomains.') ||
          originHostname.startsWith('cms65') ||
          originHostname.startsWith('webqa-.') ||
          originHostname.startsWith('atlweb-.') ||
          originHostname.startsWith('webstg-.') ||
          originHostname.endsWith('apps.atlanta1.newfoldmb.com')
        ) {
          callback(null, origin);
          return;
        }

        // Support local
        if (originHostname.includes('localhost') || originHostname.includes('127.0.0.1')) {
          callback(null, origin);
          return;
        }

        // Finally, check whitelist
        const isWhitelisted = WHITELIST.some(
          (whitelistOrigin) => new URL(whitelistOrigin).hostname === originHostname,
        );

        if (isWhitelisted) {
          callback(null, origin);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      } catch (err) {
        console.log(`There was an error inside corsOptions(): ${err} `);
        callback(null, '');
      }
    },
    credentials: true,
  };

  return corsOptions;
};

const shouldCompress = (req: Request, res: Response) => {
  if (req.headers['x-no-compression']) {
    return false;
  }

  return compression.filter(req, res);
};

/**
 * Helper function for easy subset array checking
 * @param a
 * @param b
 * @param includeType
 */
export function arrayBisSubsetOfA<T>(a: T[] = [], b: T[] = [], includeType: 'any' | 'all') {
  if (includeType === 'any') {
    // Check if at least one element of b is in a
    return b.some((item) => a.includes(item));
  } else if (includeType === 'all') {
    // Check if all elements of b are in a
    return b.every((item) => a.includes(item));
  }
  return false;
}

const getSiteId = (brand: tBrand): string | undefined => {
  switch (brand) {
    case 'HOSTGATOR':
      return '46098237';
    case 'BLUEHOST':
      return '46097236';
    default:
      return undefined;
  }
};

const getParentChannelId = (brand: tBrand): number | null => {
  switch (brand) {
    case 'HOSTGATOR':
      return 110;
    case 'BLUEHOST':
      return 263;
    case 'NETWORKSOLUTIONS':
      return 2;
    default:
      return null;
  }
};

const getRandomizedRules = (rules: iRule[], currentContainerName: string, env: Env): iRule[] => {
  const path = env.toLowerCase() === 'qa' ? 'htmlPathQA' : 'htmlPath';

  const allRules = rules.filter(
    (rule) =>
      rule.containerName === currentContainerName &&
      (!rule.weightage || rule.weightage > 0) &&
      rule.fragmentDetails[path],
  );

  allRules.forEach((rule: any) => {
    if (rule.weightage) {
      const weightage = getAdPriority(rule);
      rule.weightage = weightage;
    }
  });
  // Sort by weightage in descending order, if not available, sort by priority in ascending order
  allRules.sort((a: any, b: any) => {
    if (a.weightage && b.weightage) {
      return b.weightage - a.weightage;
    } else {
      return a.priority - b.priority;
    }
  });
  return allRules;
};

const getAdPriority = (rule: iRule): number => {
  let priority = rule?.priority;
  if (rule.weightage) {
    // Priority changes if the rule has weightage. Adding randomization and rounding it off to 2 decimal places.
    priority = parseFloat((Math.random() * rule.weightage).toFixed(2));
  }
  return priority;
};

const getGroupByRule = (rules: any[]) => {
  const grouped: (AdResponse | undefined)[] = rules.reduce(
    (acc, rule) => {
      const key = rule.productSku || rule.adIdentifier;

      if (!acc[key]) {
        acc[key] = {
          adDetails: { ...rule },
        };
      }

      return acc;
    },
    {} as Record<string, any>,
  );

  return Object.values(grouped);
};

const logSuccessResponse = (
  resolvedResponses: (AdObj | undefined)[],
  groupedRulesResponses: (AdResponse | undefined)[],
  matchingRules: iRule[],
  brand: tBrand,
  surface: string,
  env: string,
  id: string,
  rngTrace: string,
) => {
  // filter groupedRulesResponses and return only adIdentifer, alias, weightage, and priority
  const filteredGroupedRulesResponses = groupedRulesResponses.map((response) => {
    if (response?.adDetails) {
      return {
        adDetails: {
          adIdentifier: response.adDetails.adIdentifier,
          alias: response.adDetails.alias,
          weightage: response.adDetails.weightage,
          priority: response.adDetails.priority,
        },
      };
    }
    return [];
  });

  sfLogger.info(
    {
      eventType: EVENT_TYPES.Success,
      message: EVENT_MESSAGES.XSellServiceProcessed + ' ' + brand + ' ' + surface,
      env: env,
      customAttributes: {
        initiator: SFLOGGER_IDS.SFLOG_ID_38,
        brand,
        ...(surface === 'InCart' ? { accountId: id ?? '' } : { userId: id }),
        surface,
        matchingRulesCount: matchingRules.length,
        finalOffersShown: filteredGroupedRulesResponses,
        groupedRulesResponsesCount: groupedRulesResponses.length,
        resolvedResponsesCount: resolvedResponses.length,
      },
    },
    rngTrace,
  );
};

export {
  getEnv,
  getHost,
  getCorsOptions,
  getAllowListedDomains,
  shouldCompress,
  getSiteId,
  getRandomizedRules,
  isValidEnv,
  getParentChannelId,
  getAdPriority,
  getGroupByRule,
  logSuccessResponse,
  getConfigEnv,
};
