import { skus } from 'app/constants';

const isDomain = (productType: string) =>
  productType?.startsWith('domain .') ||
  productType?.startsWith('Domain Registration - .') ||
  productType === 'SFDomain' ||
  productType === 'SFDomainTransfer';

const isPrivateReg = (productCode: string) => productCode === 'PRI_REG_V2';

const isPrivateRegFreeTrial = (productCode: string, financialCdId: number) =>
  productCode === 'PRI_REG_V2' && financialCdId && financialCdId === 401;

const isLifecycleActive = (lifecycleCdId: number) => lifecycleCdId === 10;

const isEmail = (productCode: string) => {
  if (
    productCode === 'HP_E' ||
    productCode === 'E_PRO' ||
    productCode === 'E_PRO_PLUS' ||
    productCode === 'PRO_EMAIL_MIGRATION'
  ) {
    return true;
  } else if (isGoogleWorkspace(productCode) || isTitanMail(productCode)) {
    return true;
  }
  return false;
};

const isYoastSEOPremium = (productCode: string) => productCode === 'YOAST_SEO_PREMIUM';

const isSSL = (productCode: string) => {
  if (
    productCode === 'SSL_EV' ||
    productCode === 'SSL_DV' ||
    productCode === 'SSL_DV_WILDCARD' ||
    productCode === 'SSL_DV_MONTHLY' ||
    productCode === 'SSL_PRO' ||
    productCode === 'SSL_BASIC' ||
    productCode === 'SSL_DV_NETSOL_RCOM_WEBCOM' ||
    productCode === 'SSL_BASIC_NETSOL_RCOM_WEBCOM' ||
    productCode === 'SSL_PRO_NETSOL_RCOM_WEBCOM' ||
    productCode === 'SSL_WILDCARD_NETSOL_RCOM_WEBCOM' ||
    productCode === 'SSL_EV_NETSOL_RCOM_WEBCOM'
  ) {
    return true;
  }
  return false;
};

const isHosting = (productCode: string) => {
  if (
    productCode &&
    (productCode.startsWith('HP_PKG') ||
      productCode.startsWith('BH_DEDI_') ||
      productCode.startsWith('BH_VPS') ||
      productCode.startsWith('BH_DEDI') ||
      productCode.startsWith('BH_PKG_WP_') ||
      productCode.startsWith('BH_HP_PKG_') ||
      productCode === 'BH_ECOMM_STORE' ||
      productCode === 'BH_ECOMM_STORE_MKTPL' ||
      productCode.startsWith('BH_RESELLER_HP') ||
      productCode.startsWith('BH_SHRD_WIN') ||
      productCode.startsWith('DIY_WS_WEEBLY_') ||
      productCode.startsWith('DIY_WS_WEBZAI_') ||
      productCode === 'BH_MWP_BUILD' ||
      productCode === 'BH_MWP_GROW' ||
      productCode === 'BH_MWP_SCALE' ||
      productCode.startsWith('DIFM_STANDARD') ||
      productCode.startsWith('DIFM_PREMIUM') ||
      productCode === 'PRO_SERV_WEBSITE_ECOMM' ||
      productCode === 'PRO_WEBSITE_PROD' ||
      productCode.startsWith('WP_CLOUD_') ||
      productCode.startsWith('WCOM_HP_PKG_') ||
      productCode.startsWith('BH_SH_PKG_DESTINY_') ||
      isSolutionHosting(productCode))
  ) {
    return true;
  }
  return false;
};

const isSharedHosting = (productCode: string) => {
  if (
    productCode &&
    (productCode === 'HP_PKG_START_UNX' ||
      productCode === 'HP_PKG_START_WIN' ||
      productCode === 'HP_PKG_S_UNX' ||
      productCode === 'HP_PKG_S_WIN' ||
      productCode === 'HP_PKG_A_UNX' ||
      productCode === 'HP_PKG_A_WIN' ||
      productCode === 'HP_PKG_P_UNX' ||
      productCode === 'HP_PKG_P_WIN' ||
      productCode === 'HP_PKG_WEB_P_UNX' ||
      productCode === 'HP_PKG_WEB_S_UNX' ||
      productCode === 'HP_PKG_WEB_A_UNX' ||
      productCode === 'HP_PKG_WEB_P_UNX_V2' ||
      productCode === 'HP_PKG_WEB_S_UNX_V2' ||
      productCode === 'HP_PKG_WEB_A_UNX_V2' ||
      productCode.startsWith('BH_PKG_WP_') ||
      productCode.startsWith('BH_HP_PKG_') ||
      productCode.startsWith('BH_SH_PKG_DESTINY_'))
  ) {
    return true;
  } else {
    return false;
  }
};

const isWPCloud = (productCode: string) => {
  if (productCode && productCode.startsWith('WP_CLOUD_')) {
    return true;
  } else {
    return false;
  }
};

const isWordpressSolutions = (productCode: string) => {
  if (
    productCode &&
    (productCode === 'WP_SOLUTION_CREATOR' ||
      productCode === 'WP_SOLUTION_COMMERCE' ||
      productCode === 'WP_SOLUTION_SERVICE')
  ) {
    return true;
  } else {
    return false;
  }
};

const isEcommerceHosting = (productCode: string) => {
  if (
    productCode &&
    (productCode === 'MYSCHEDULER_COMPONENT' ||
      productCode === 'SB_RT_BASIC' ||
      isOnlineMarketplace(productCode) ||
      isOnlineStore(productCode) ||
      isEcommDash(productCode) ||
      productCode.startsWith('ECOMM_') ||
      productCode.startsWith('ECOMM_PRO_') ||
      productCode.startsWith('WCOM_ECOMM_') ||
      productCode === 'BH_ECOMM_STORE' ||
      productCode === 'BH_ECOMM_STORE_MKTPL')
  ) {
    return true;
  } else {
    return false;
  }
};

const isPaidSitelock = (productCode: string, financialCdId: number) => {
  if (productCode && productCode.startsWith('SITELOCK_') && financialCdId !== 401) {
    return true;
  } else {
    return false;
  }
};

const isProProduct = (prodCode: string): boolean => {
  return prodCode.startsWith('PRO_');
};

const isProWebsite = (prodCode: string): boolean => {
  return prodCode.startsWith('PRO_WEBSITE_PROD');
};

const isProOnlineStore = (prodCode: string): boolean => {
  return prodCode.startsWith('PRO_SERV_WEBSITE_ECOMM');
};

const isYodleCustomer = (prodCode: string): boolean => {
  const { WEB_PPC_YODLE, WEB_WEBSITE_YODLE } = skus;
  return prodCode === WEB_PPC_YODLE || prodCode === WEB_WEBSITE_YODLE;
};

const isBasicHosting = (prodCode: string): boolean => {
  const { BH_PKG_WP_ENT, BH_HP_PKG_STARTER, BH_SH_PKG_DESTINY_STARTER } = skus;
  return prodCode === BH_PKG_WP_ENT || prodCode === BH_HP_PKG_STARTER || prodCode === BH_SH_PKG_DESTINY_STARTER;
};

const isPlusHosting = (prodCode: string): boolean => {
  const { BH_PKG_WP_PLUS, BH_HP_PKG_PLUS, BH_SH_PKG_DESTINY_PLUS } = skus;
  return prodCode === BH_PKG_WP_PLUS || prodCode === BH_HP_PKG_PLUS || prodCode === BH_SH_PKG_DESTINY_PLUS;
};

const isChoicePlus = (prodCode: string): boolean => {
  const { BH_PKG_WP_CHOICE_PLUS, BH_HP_PKG_BUSINESS, BH_SH_PKG_DESTINY_BUSINESS } = skus;
  return prodCode === BH_PKG_WP_CHOICE_PLUS || prodCode === BH_HP_PKG_BUSINESS || prodCode === BH_SH_PKG_DESTINY_BUSINESS;
};

const isProHosting = (prodCode: string): boolean => {
  const { BH_PKG_WP_PRO, BH_HP_PKG_PRO, BH_SH_PKG_DESTINY_PRO } = skus;
  return prodCode === BH_PKG_WP_PRO || prodCode === BH_HP_PKG_PRO || prodCode === BH_SH_PKG_DESTINY_PRO;
};

const isHighPerformanceHosting = (productCode: string): boolean => {
  if (
    productCode &&
    (productCode === 'BH_HP_PKG_PREMIUM' ||
      productCode === 'BH_HP_PKG_ENHANCED' ||
      productCode === 'BH_HP_PKG_ELITE' ||
      productCode === 'BH_SH_PKG_DESTINY_PREMIUM' ||
      productCode === 'BH_SH_PKG_DESTINY_ENHANCED' ||
      productCode === 'BH_SH_PKG_DESTINY_ELITE')
  ) {
    return true;
  } else {
    return false;
  }
};

const isSolutionHosting = (productCode: string) => {
  if (
    productCode &&
    (productCode === 'BH_CREATOR_SOLUTION_HOSTING' ||
      productCode === 'BH_COMMERCE_SOLUTION_HOSTING' ||
      productCode === 'BH_SH_PKG_DESTINY_CREATOR' ||
      productCode === 'BH_SH_PKG_DESTINY_SOLUTION')
  ) {
    return true;
  } else {
    return false;
  }
};

const isGoogleWorkspace = (productCode: string) => {
  if (
    productCode &&
    (productCode === 'GOOGLE_WORKSPACE_STARTER' ||
      productCode === 'GOOGLE_WORKSPACE_STANDARD' ||
      productCode === 'GOOGLE_WORKSPACE_PLUS')
  ) {
    return true;
  } else {
    return false;
  }
};

const isTitanMail = (productCode: string) => {
  if (
    productCode &&
    (productCode === 'TITAN_MAIL_PRO' ||
      productCode === 'TITAN_MAIL_STD' ||
      productCode === 'TITAN_MAIL_TRIAL')
  ) {
    return true;
  } else {
    return false;
  }
};

const isWebsiteTrial = (productCode: string) => {
  return productCode === 'WEBSITE_TRIAL';
};

const isBusinessDirectory = (productCode: string) =>
  productCode && productCode.startsWith('BIZ_DIR_');

const isBuilder = (productCode: string) => {
  return (
    productCode &&
    (productCode === 'WEBSITE' ||
      productCode.startsWith('WEBSITE_') ||
      productCode.startsWith('WCOM_WEBSITE_') ||
      productCode.startsWith('DIY_'))
  );
};

// for exclusions to MySchedulr: WEBSITE, and WEBSITE_L (when builder is S+)
const isBuilderSplus = (productCode: string) => {
  return productCode && (productCode === 'WEBSITE' || productCode === 'WEBSITE_L');
};

// check if its domain expiration protection
const isDEP = (productCode: string) =>
  productCode && (productCode === 'DOM_EXP_PROTECT' || productCode === 'DEP_NOSYNC');

const isBusinessEssential = (productCode: string) =>
  productCode && productCode === 'BUSINESS_ESSENTIAL';

const isEcommDash = (productCode: string) => productCode && productCode.startsWith('ECOMDASH_');

const isWebsiteMarketing = (productCode: string) =>
  productCode && productCode === 'WEBSITE_MARKETING';

const isOnlineStore = (productCode: string) => productCode && productCode === 'ONLINE_STORE';

const isOnlineMarketplace = (productCode: string) =>
  productCode && productCode === 'ONLINE_MARKETPLACE';

const isDIFMHosting = (productCode: string) => productCode && productCode.startsWith('DIFM_');

const isAlternateDomain = (productCode: string) => productCode && productCode === 'ALT_DOMAINS';

const isEcommMyScheduler = (productCode: string) =>
  productCode && productCode === 'ECOMDASH_MYSCHEDULER';

const isEcommEssential = (productCode: string) =>
  productCode && productCode === 'ECOMDASH_ESSENTIAL';

const isEcommPremium = (productCode: string) => productCode && productCode === 'ECOMDASH_PREMIUM';

const isEcommEssentialUnbundled = (productCode: string) =>
  productCode && productCode === 'ECOMDASH_ESSENTIAL_UNBUNDLED';

const isEcommPremiumUnbundled = (productCode: string) =>
  productCode && productCode === 'ECOMDASH_PREMIUM_UNBUNDLED';

const isWordpressHostingNetsol = (productCode: string) =>
  productCode &&
  (productCode.startsWith('HP_PKG_WP_ENT') ||
    productCode.startsWith('HP_PKG_WP_BIZ') ||
    productCode.startsWith('HP_PKG_WP_PRO'));

export {
  isSolutionHosting,
  isBasicHosting,
  isBuilder,
  isBusinessDirectory,
  isChoicePlus,
  isDEP,
  isDomain,
  isEcommerceHosting,
  isEmail,
  isGoogleWorkspace,
  isHosting,
  isLifecycleActive,
  isPaidSitelock,
  isPrivateReg,
  isPrivateRegFreeTrial,
  isProHosting,
  isProProduct,
  isProWebsite,
  isProOnlineStore,
  isSharedHosting,
  isSSL,
  isTitanMail,
  isWebsiteTrial,
  isWordpressSolutions,
  isYoastSEOPremium,
  isYodleCustomer,
  isPlusHosting,
  isWPCloud,
  isBusinessEssential,
  isEcommDash,
  isWebsiteMarketing,
  isOnlineStore,
  isOnlineMarketplace,
  isDIFMHosting,
  isAlternateDomain,
  isBuilderSplus,
  isHighPerformanceHosting,
  isEcommMyScheduler,
  isEcommEssential,
  isEcommPremium,
  isEcommEssentialUnbundled,
  isEcommPremiumUnbundled,
  isWordpressHostingNetsol,
};
