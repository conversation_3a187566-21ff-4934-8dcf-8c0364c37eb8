import { fgProductLite } from '../models/index';
import {
  isDomain,
  isEcommerceHosting,
  isEmail,
  isHosting,
  isPaidSitelock,
  isPrivateReg,
  isPrivateRegFreeTrial,
  isProProduct,
  isSharedHosting,
  isSSL,
  isWordpressSolutions,
  isYoastSEOPremium,
  isYodleCustomer,
  isBasicHosting,
  isChoicePlus,
  isProHosting,
  isBusinessDirectory,
  isWebsiteTrial,
  isDEP,
  isBuilder,
  isTitanMail,
  isPlusHosting,
  isWPCloud,
  isGoogleWorkspace,
  isSolutionHosting,
  isBusinessEssential,
  isOnlineMarketplace,
  isOnlineStore,
  isEcommDash,
  isWebsiteMarketing,
  isDIFMHosting,
  isProOnlineStore,
  isProWebsite,
  isBuilderSplus,
  isHighPerformanceHosting,
  isEcommMyScheduler,
  isEcommEssential,
  isEcommPremium,
  isEcommEssentialUnbundled,
  isEcommPremiumUnbundled,
  isWordpressHostingNetsol,
} from './productIdentifiers';
import { BUSINESS_RULE_PRODUCT_KEYS as productKeys, SFLOGGER_IDS, skus } from '../constants';
import { sfLogger } from 'app/logger';
import { EVENT_TYPES } from 'app/constants';

export class BusinessRuleHelper {
  constructor() {
    this.productLookup = new Map<string, number>();
    this.domainList = new Array<fgProductLite>();
    this.gwsDomainList = new Array<string>();
    this.hostingList = new Array<fgProductLite>();
  }

  private productLookup;

  private domainList;
  private gwsDomainList;
  private hostingList;

  public getDomainsForUser() {
    return this.domainList;
  }
  public getGwsDomainsForUser() {
    return this.gwsDomainList;
  }

  public getHostingForUser() {
    return this.hostingList;
  }

  public loadProductMap(activeProducts: fgProductLite[]) {
    this.initializeLookups();
    const today = new Date();

    if (activeProducts && activeProducts.length) {
      activeProducts.forEach((product) => {
        if (isDomain(product.prodType)) {
          this.domainList.push(product);
          this.incrementProductCounter(productKeys.activeDomains);
        } else if (isEcommerceHosting(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeEcommHosting);
          this.hostingList.push(product);
          if (this.isPurchasedDuringPastDayRange(today, product, 7)) {
            this.incrementProductCounter(productKeys.purchEcommLast7Days);
          }

          // these are subset of ecommerce
          if (isOnlineMarketplace(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeOnlineMarketplace);
          } else if (isOnlineStore(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeOnlineStore);
          } else if (isEcommDash(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeEcommDash);

            // if (isEcommMyScheduler(product.prodCode)) {
            //   this.incrementProductCounter(productKeys.activeEcommMySchedular);
            // } else if (isEcommEssential(product.prodCode)) {
            //   this.incrementProductCounter(productKeys.activeEcommEssential);
            // } else if (isEcommPremium(product.prodCode)) {
            //   this.incrementProductCounter(productKeys.activeEcommPremium);
            // } else if (isEcommEssentialUnbundled(product.prodCode)) {
            //   this.incrementProductCounter(productKeys.activeEcommEssentialUnbundled);
            // } else if (isEcommPremiumUnbundled(product.prodCode)) {
            //   this.incrementProductCounter(productKeys.activeEcommPremiumUnbundled);
            // }
          }
        } else if (isHosting(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeHosting);
          this.hostingList.push(product);

          if (this.isPurchasedDuringPastDayRange(today, product, 7)) {
            this.incrementProductCounter(productKeys.purchHostingLast7Days);
          }

          // shared hosting is a subset of hosting
          if (isSharedHosting(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeSharedHosting);
          }
          if (isBasicHosting(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeBasicHosting);
          } else if (isChoicePlus(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeChoicePlus);
          } else if (isProHosting(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeProHosting);
          } else if (isPlusHosting(product.prodCode)) {
            this.incrementProductCounter(productKeys.activePlusHosting);
          } else if (isWPCloud(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeWPCloud);
          } else if (isSolutionHosting(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeSolutionHosting);
          } else if (isDIFMHosting(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeDIFMHosting);
          } else if (isProOnlineStore(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeProOnlineStore);
          } else if (isProWebsite(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeProWebsite);
          } else if (isHighPerformanceHosting(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeHighPerformanceHosting);
          } else if (isWordpressHostingNetsol(productKeys.activeWordpressHostingNetsol)) {
            this.incrementProductCounter(productKeys.activeWordpressHostingNetsol);
          }
        } else if (isPaidSitelock(product.prodCode, product.financialCdId)) {
          this.incrementProductCounter(productKeys.activePaidSitelock);
        } else if (isPrivateReg(product.prodCode)) {
          this.incrementProductCounter(productKeys.activePR);

          // uses same product code as normal PR
          if (isPrivateRegFreeTrial(product.prodCode, product.financialCdId)) {
            this.incrementProductCounter(productKeys.activeFreeTrialPR);
          }
        } else if (isEmail(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeEmail);

          if (isGoogleWorkspace(product.prodCode)) {
            this.gwsDomainList.push(product.prodInstName);
          }
          // titan mail is a subset of email.
          if (isTitanMail(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeTitanMail);
          }
        } else if (isSSL(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeSSL);
        } else if (isWordpressSolutions(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeWordpressSolutions);
        } else if (isYoastSEOPremium(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeYoastSEOPremium);
        } else if (isProProduct(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeProProducts);
        } else if (isYodleCustomer(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeYodleCustomers);
        } else if (isWebsiteTrial(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeWebsiteTrial);
        } else if (isBusinessDirectory(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeBusinessDirectories);
        } else if (isDEP(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeDEP);
        } else if (isBuilder(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeBuilder);
          // a subset of builder.
          if (isBuilderSplus(product.prodCode)) {
            this.incrementProductCounter(productKeys.activeBuilderSplus);
          }
        } else if (isBusinessEssential(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeBusinessEssential);
        } else if (isWebsiteMarketing(product.prodCode)) {
          this.incrementProductCounter(productKeys.activeWebsiteMarketing);
        }
      });
    }
  }

  convertMonthToNumber = (monthName: string) => {
    let monthNumber = 0;
    switch (monthName) {
      case '01':
      case 'January':
        monthNumber = 0;
        break;
      case '02':
      case 'February':
        monthNumber = 1;
        break;
      case '03':
      case 'March':
        monthNumber = 2;
        break;
      case '04':
      case 'April':
        monthNumber = 3;
        break;
      case '05':
      case 'May':
        monthNumber = 4;
        break;
      case '06':
      case 'June':
        monthNumber = 5;
        break;
      case '07':
      case 'July':
        monthNumber = 6;
        break;
      case '08':
      case 'August':
        monthNumber = 7;
        break;
      case '09':
      case 'September':
        monthNumber = 8;
        break;
      case '10':
      case 'October':
        monthNumber = 9;
        break;
      case '11':
      case 'November':
        monthNumber = 10;
        break;
      case '12':
      case 'December':
        monthNumber = 11;
        break;
      default:
    }
    return monthNumber;
  };

  isPurchasedDuringPastDayRange = (today: Date, product: fgProductLite, days: number) => {
    let result = false;
    let dateStr = '';
    // if createdDate is null just skip this check

    if (product && product.createdDate) {
      try {
        const ONE_DAY = 86400000;

        // seeing this example in productLite result so far
        // "createdDate": "2024-07-19T05:19:09-04:00"
        const firstSeparator = product.createdDate.indexOf('T');

        dateStr = product.createdDate.substring(0, firstSeparator);

        let parts = dateStr.split('-');
        let year: number = Number(parts[0]);
        let month: number = this.convertMonthToNumber(parts[1]);
        let day: number = Number(parts[2]);

        // Please pay attention to the month (parts[1]);
        //  JavaScript counts months from 0:
        // January - 0, February - 1, etc.
        let myCreatedDate = new Date(year, month, day);

        let maxDate = myCreatedDate;
        maxDate.setTime(maxDate.getTime() + ONE_DAY * days);

        if (today.getTime() > maxDate.getTime()) {
          // purchased too long ago
          result = false;
        } else {
          // purchased during past # days
          result = true;
        }
        //console.log("todays date = " + today.toDateString());
        //console.log("created date + 7 = " + maxDate.toDateString());
      } catch (err: any) {
        console.error(
          `problem converting string to date for createdDate (${dateStr}): ` + err.message,
        );
        sfLogger.error(
          {
            eventType: EVENT_TYPES.ADAM_BusinessRulesHelper,
            message: `problem converting string to date for createdDate`,
            errorDetails: err.message,
            customAttributes: {
              initiator: SFLOGGER_IDS.SFLOG_ID_09,
              dateStr,
              days,
              today,
            },
          },
          '',
        );
      }
    }
    return result;
  };

  incrementProductCounter = (key: string) => {
    let count = this.productLookup.get(key);
    if (count === undefined) {
      throw new Error(
        `businessRuleHelper.incrementProductCounter() - The key ${key} was not recognized`,
      );
    }
    count++;
    this.productLookup.set(key, count);
  };

  initializeLookups = () => {
    this.productLookup.set(productKeys.activeDomains, 0);
    this.productLookup.set(productKeys.activePR, 0);
    this.productLookup.set(productKeys.activeFreeTrialPR, 0);
    this.productLookup.set(productKeys.activeEmail, 0);
    this.productLookup.set(productKeys.activeHosting, 0);
    this.productLookup.set(productKeys.activePaidSitelock, 0);
    this.productLookup.set(productKeys.activeSSL, 0);
    this.productLookup.set(productKeys.activeSharedHosting, 0);
    this.productLookup.set(productKeys.activeWordpressSolutions, 0);
    this.productLookup.set(productKeys.activeEcommHosting, 0);
    this.productLookup.set(productKeys.activeYoastSEOPremium, 0);
    this.productLookup.set(productKeys.purchHostingLast7Days, 0);
    this.productLookup.set(productKeys.purchEcommLast7Days, 0);
    this.productLookup.set(productKeys.activeProProducts, 0);
    this.productLookup.set(productKeys.activeYodleCustomers, 0);
    this.productLookup.set(productKeys.activeBasicHosting, 0);
    this.productLookup.set(productKeys.activeChoicePlus, 0);
    this.productLookup.set(productKeys.activeProHosting, 0);
    this.productLookup.set(productKeys.activeWebsiteTrial, 0);
    this.productLookup.set(productKeys.activeBusinessDirectories, 0);
    this.productLookup.set(productKeys.activeDEP, 0);
    this.productLookup.set(productKeys.activeBuilder, 0);
    this.productLookup.set(productKeys.activeTitanMail, 0);
    this.productLookup.set(productKeys.activePlusHosting, 0);
    this.productLookup.set(productKeys.activeWPCloud, 0);
    this.productLookup.set(productKeys.activeSolutionHosting, 0);
    this.productLookup.set(productKeys.activeOnlineMarketplace, 0);
    this.productLookup.set(productKeys.activeOnlineStore, 0);
    this.productLookup.set(productKeys.activeBusinessEssential, 0);
    this.productLookup.set(productKeys.activeEcommDash, 0);
    this.productLookup.set(productKeys.activeWebsiteMarketing, 0);
    this.productLookup.set(productKeys.activeDIFMHosting, 0);
    this.productLookup.set(productKeys.activeProOnlineStore, 0);
    this.productLookup.set(productKeys.activeProWebsite, 0);
    this.productLookup.set(productKeys.activeBuilderSplus, 0);
    this.productLookup.set(productKeys.activeHighPerformanceHosting, 0);
    this.productLookup.set(productKeys.activeEcommMySchedular, 0);
    this.productLookup.set(productKeys.activeEcommEssential, 0);
    this.productLookup.set(productKeys.activeEcommPremium, 0);
    this.productLookup.set(productKeys.activeEcommEssentialUnbundled, 0);
    this.productLookup.set(productKeys.activeEcommPremiumUnbundled, 0);
    this.productLookup.set(productKeys.activeWordpressHostingNetsol, 0);
  };

  public getProductCount = (key: string) => {
    let result = this.productLookup.get(key);
    if (result === undefined) {
      throw new Error(`businessRuleHelper.getProductCount() - key ${key} is not recognized`);
    } else {
      if (process.env.LOCAL_LOGS === 'true') {
        console.log(`count of ${key} = ${result}`);
      }
      return result;
    }
  };
}

export default BusinessRuleHelper;
