import { tBrand } from 'app/models/aemRules';
import { AMWI, AMHPCards, AM_BUSINESS_RULES } from '../constants';

/**
 * This helper is focussed on checking ad rules specific
 * to certain countries
 * 
 * @param countryCode 
 * @param actionType
 * @param surface
 * @param businessRule
 * @returns boolean
 */
const isAdAllowedForCountry = (
  countryCode: string,
  actionType: string,
  surface: string,
  businessRule: string,
): boolean => {
  let isAllowed: boolean = true;

  if (countryCode?.toUpperCase() === 'IN') {
    if(actionType === 'openCTB') {
      // Not returning ads that open CTB when country code is IN
      isAllowed = false;
    }
    else if(businessRule === AM_BUSINESS_RULES.WI_PRO_WEBSITE 
        || businessRule === AM_BUSINESS_RULES.HOME_PRO_WEBSITE) {
      // pro serv not allowed for India
      isAllowed = false;
    }
  }

  return isAllowed;
};

export { isAdAllowedForCountry };
