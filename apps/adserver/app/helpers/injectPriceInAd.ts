import { FREE } from 'app/constants';
import { getProductPrice, iPricingBffTerm } from './pricingBffHelper';
import { ProductPrice } from 'app/models/aemRules';

/**
 * Helper function to fetch the price for the ad and inject in the html
 * @param ad
 * @param term
 * @param sku
 * @param brand
 * @returns
 */
export const injectPriceInAd = async (
  ad: string,
  pricingTerm: string | undefined,
  pricingDisplayTerm: iPricingBffTerm,
  coupon: string,
  skus: string | string[],
  brand: string,
  currencyCode: string,
  pricingSiteId: number | undefined,
  env: string,
  statusMessages: string[],
  containerName: string,
): Promise<string> => {
  try {
    ad = ad.replace(/(\r\n|\n|\r)/gm, ''); //remove the random \n and \r coz of AEM's editor
  }
  catch(err: any) {
    const skusOutput = Array.isArray(skus) ? skus.toString() : skus;
    throw new Error(`injectPriceInAd: Error when trying to prepare html in ad for sku ${skusOutput} surface ${containerName}: ${err.message}, check if the experience fragment exists.`);
  }

  if (ad?.includes('__PRICE__AD__')) {
    const priceTerm = pricingTerm?.toLowerCase() as iPricingBffTerm;
    const skusForPricing = Array.isArray(skus) ? skus : [skus];

    for (let i = 0; i < skusForPricing.length; i++) {
      const sku = skusForPricing[i];
      let priceToDisplay: string = '';
      let basePriceToDisplay: string = '';
      const pricingSkuPlaceholder = sku.split('_').at(-1);
      let productPrice: ProductPrice = await getProductPrice(
        sku,
        brand,
        priceTerm,
        pricingDisplayTerm,
        coupon,
        currencyCode,
        pricingSiteId,
        env,
        statusMessages,
        containerName,
      );

      // Cases where we don't want to show the pricing term.
      if (productPrice?.price === FREE || priceTerm === 'one time shipped' || !priceTerm) {
        priceToDisplay = productPrice?.price ?? '';
        basePriceToDisplay = productPrice?.basePrice ?? '';
      } else if (productPrice?.price) {
        priceToDisplay = productPrice?.price + getDisplayTerm(pricingDisplayTerm);
        // Same display term for the base price, if it exists.
        if (productPrice?.basePrice) {
          basePriceToDisplay = productPrice?.basePrice + getDisplayTerm(pricingDisplayTerm);
        }
      }
      if (pricingSkuPlaceholder) {
        ad = ad.replaceAll(`__PRICE__${pricingSkuPlaceholder}__AD__`, priceToDisplay);
      }
      ad = ad.replaceAll('__ORIGINAL__AD__PRICE__', basePriceToDisplay);
      ad = ad.replaceAll('__PRICE__AD__', priceToDisplay);
    }
  }
  return ad;
};

export const getDisplayTerm = (priceDisplayTerm: iPricingBffTerm) => {
  switch (priceDisplayTerm) {
    case 'month':
      return '/mo';
    case 'year':
      return '/yr';
    case 'one time shipped':
    default:
      return '';
  }
};
