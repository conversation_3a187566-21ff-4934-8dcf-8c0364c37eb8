import {
  LOGGED_IN_STATUS_ALLOWED_LOGGED_IN,
  LOGGED_IN_STATUS_ALLOWED_NOT_LOGGED_IN,
  LOGGED_IN_STATUS_ALLOWED_ANY,
} from 'app/constants';

// Compare user logged in status to the adrule attribute loggedInStatusRequired.
// by default we should assume if loggedInStatusRequired is not configured, it should
// default to 'any'
const isLoggedInStatusAcceptable = (isLoggedIn: boolean, loggedInStatusRequired: string) => {
  let isAcceptable: boolean;

  if (isLoggedIn) {
    if (
      !loggedInStatusRequired ||
      loggedInStatusRequired === LOGGED_IN_STATUS_ALLOWED_ANY ||
      loggedInStatusRequired === LOGGED_IN_STATUS_ALLOWED_LOGGED_IN
    ) {
      isAcceptable = true;
    } else {
      isAcceptable = false;
    }
  } else {
    if (
      !loggedInStatusRequired ||
      loggedInStatusRequired === LOGGED_IN_STATUS_ALLOWED_ANY ||
      loggedInStatusRequired === LOGGED_IN_STATUS_ALLOWED_NOT_LOGGED_IN
    ) {
      isAcceptable = true;
    } else {
      isAcceptable = false;
    }
  }

  return isAcceptable;
};

export { isLoggedInStatusAcceptable };
