import configFile from '../config/config.json';
import { environmentConfig } from '../models/index';

/* grab any info from the config.json specified for the env */
export function getConfigForEnv(env: string): environmentConfig | undefined {
  let customInfo: environmentConfig | undefined = undefined;
  if (configFile.env) {
    // eslint-disable-next-line default-case
    switch (env) {
      case '':
      case 'development':
        if (configFile.env.development) {
          customInfo = configFile.env.development;
        }
        break;
      case 'qa':
        if (configFile.env.qa) {
          customInfo = configFile.env.qa;
        }
        break;
      case 'jarvisqa1':
        if (configFile.env.jarvisqa1) {
          customInfo = configFile.env.jarvisqa1;
        }
        break;
      case 'stg':
        if (configFile.env.stg) {
          customInfo = configFile.env.stg;
        }
        break;
      case 'prod':
      case 'production':
        if (configFile.env.production) {
          customInfo = configFile.env.production;
        }
        break;
    }
  }
  return customInfo;
}
