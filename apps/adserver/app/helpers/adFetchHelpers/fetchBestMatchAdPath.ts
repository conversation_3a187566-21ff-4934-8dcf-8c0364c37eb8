import { Elements, Entity, iAdMatchers, tTemplateTokenOverrides } from 'app/models';
import { AxiosInstance } from 'axios';
import { getAdProperties, matchHostname } from './adRefineUtils';
import { tLogDecision } from 'app/fetchers/aem/ads';

export const fetchBestMatchAdPath = async (
  brandClient: AxiosInstance,
  adTokenConfig: any,
  logDecision: tLogDecision,
  bestMatch: undefined | Elements,
  useDisabledAds: string | boolean,
  referer: string | undefined,
  host: string,
  path: string,
  tags: string,
) => {
  try {
    let response;

    const aemRequestPath = '/api/assets/ad-server.json';
    let adMatch: boolean = false;
    // MetaData on specific ads
    let adTags;
    let adPriority;
    let adConfig: any;
    let adPlacement: Elements;
    const requestHostName = referer || `${host}${path}`;
    const adMatches: iAdMatchers = {};
    let highestMatchedPriority: number = 0;

    // We should ad some caching here
    response = await brandClient.get(aemRequestPath);
    const adplacements = response.data.entities as Entity[];
    console.log(`Look for ads that match request: ${requestHostName}`);
    logDecision(`Look for ads that match request: ${requestHostName}`);

    for (const thisAd of adplacements) {
      console.log('STEP THROUGH AD');
      adMatch = false;
      // Reset for this iteration
      adTokenConfig = '';

      logDecision(
        `<p \>Processing ad: title=${thisAd.properties?.title}, name=${thisAd.properties?.name}`,
      );
      // Skip this ad if currently disabled
      if (!useDisabledAds && thisAd.properties.elements.disabled?.value === true) {
        logDecision(`Ad disabled, skipping`);
        continue;
      } else if (useDisabledAds && thisAd.properties.elements.disabled?.value === true) {
        logDecision(
          `We are allowing disabled Ads, so we will process this ad for testing, but would not in production`,
        );
      }

      // Ad placements are determined by specificity. This is the order, starting with most specific (most specific ad wins)
      // 1. Exact URL match: https://www.bluehost.com/help/article/test === https://www.bluehost.com/help/article/test
      // 2. Tag match:  "email" == (referer === https://www.bluehost.com/help/article/test?tags=email)
      // 3. Days Remaining:  This optional field can be used to pick one ad over another to offer a different discount
      //                  < 10 days left on free trial versus >= 10 days versus expired free trial
      // 4. Wildcard match: https://www.bluehost.com/help/article/test matches https://www.bluehost.com/help/article/*
      adConfig = JSON.parse(thisAd.properties.elements.adConfig.value);
      adPlacement = thisAd.properties.elements;

      const [adTags, adPriority = 0] = getAdProperties(thisAd.properties, logDecision);

      // Step through URLs returned
      if (adConfig?.urls?.length && !bestMatch) {
        for (const uIndex in adConfig.urls) {
          // If we already found a best match, we can stop processing items
          // if (bestMatch) {
          //   logDecision(
          //     `We already have a best match and can skip processing this ad's other URLs`,
          //   );
          //   continue;
          // }

          adMatch = false; // reset matcher

          // Test for Exact match, if found, it wins
          console.log(
            `Look for exact match between ${adConfig.urls[uIndex]} === ${requestHostName} `,
          );
          logDecision(
            `Look for exact match between ${adConfig.urls[uIndex]} === ${requestHostName}`,
          );

          if (adConfig.urls[uIndex] === requestHostName) {
            bestMatch = adPlacement;
            logDecision(`Found exact URL match for this add on URL ${adConfig.urls[uIndex]}`);
          }

          // Test for wildcard matches
          try {
            adMatch = matchHostname(requestHostName, adConfig.urls[uIndex]);
          } catch (err) {
            console.log(err);
          }

          if (adMatch) {
            console.log(`Wildcard Match found ${adConfig.urls[uIndex]} === ${requestHostName}`);
            logDecision(`Wildcard Match found ${adConfig.urls[uIndex]} === ${requestHostName}`);
            adMatches.wildcard = adPlacement;
          }
        }
      }
      // Step through all tags returned in this ad.
      if (adTags && adTags.length && tags && tags.length) {
        let tagParams = [];

        // Need to handle cases of when one query string called tag or multiple are passed in
        if (typeof tags === 'string') {
          tagParams.push(tags);
        } else {
          tagParams = tags; // map multiple
        }

        // Step through tag array from the ad Config
        for (const index in adTags) {
          // Need to loop through the tags passed from the client and look for matches
          for (const cindex in tagParams) {
            const tag = adTags[index];

            logDecision(
              `Test Tags AdTag ${tag.toLowerCase()} === Param Tag ${tagParams[
                cindex
              ].toLowerCase()}`,
            );
            // Test for tag match
            if (tag.toLowerCase() === tagParams[cindex].toLowerCase()) {
              logDecision(`Matched tag ${tag.toLowerCase()}`);

              // Priority is used to resolve conflicts and can be set in config
              if (highestMatchedPriority <= adPriority) {
                adMatch = true;
                adMatches.tags = adPlacement;
                logDecision(`New highest priority match ${highestMatchedPriority} < ${adPriority}`);
                highestMatchedPriority = adPriority;
              } else {
                logDecision(
                  `Skipping because lower priority ad ${highestMatchedPriority} > ${adPriority}`,
                );
              }
            }
          }
        }
      }
    }

    // Determine best match
    // If we already have a value, it was an exact URL match and it wins
    // If we don't have a bestMatch, we have to look at matches we found for a best match
    if (!bestMatch) {
      bestMatch = adMatches.tags || adMatches.wildcard;
      let matchType;
      if (adMatches.tags) {
        matchType = 'Tags';
      } else if (adMatches.wildcard) {
        matchType = 'Wildcard';
      }

      if (matchType) {
        logDecision(`Best Match ${bestMatch} matched by ${matchType}`);
        console.log(
          `Our best match for ad is located at ${JSON.stringify(
            bestMatch,
            null,
            '\t',
          )} was it wildcard: ${JSON.stringify(adMatches.wildcard)}`,
        );
      }
    } else {
      logDecision(`Best Match ${bestMatch} matched by exact URL`);
    }
    return { response, hasError: false, bestMatch };
  } catch (err) {
    console.log(`Error Making call to AEM ${JSON.stringify(err, null, '\t')}`);
    return { response: null, hasError: err, bestMatch };
  }
};
