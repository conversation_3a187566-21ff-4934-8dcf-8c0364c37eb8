import { tLogDecision } from 'app/fetchers/aem/ads';
import { Elements, Entity, tTemplateTokenOverrides } from 'app/models';
import { AxiosInstance } from 'axios';
import * as cheerio from 'cheerio';

/**
 * This function does the refinement of the best match ad's HTML by
 * replacing tokens and converting relative URLs to absolute.
 * @param bestMatch
 * @param brandClient
 * @param adTokenConfig
 * @param templateTokensOverrides
 * @param logDecision
 * @returns
 */
export async function fetchAndRefineBestMatchAd(
  bestMatch: Elements,
  brandClient: AxiosInstance,
  adTokenConfig: any,
  templateTokensOverrides: tTemplateTokenOverrides[],
  logDecision: tLogDecision,
  brand: string,
  env: string,
) {
  let retVal;
  let adPath;
  let response;

  // Bestmatch object comes from adServer
  if (typeof bestMatch === 'object') {
    adPath = bestMatch.adPlacement.value;
  } else {
    // bestmatch string could come from a direct URL request for ad, bypasses ad server
    adPath = bestMatch;
  }

  /**
   * SOFT-167040: The AEM team came to us on a Friday night
   * to release this the next Wednesday..... much planning
   *
   * We might have to remove it later so i am doing it like this
   * They will pass env=qa for their QA envs but we dont want to mess
   * up our own data fetching
   */
  if (env && env === 'qa') {
    const stubs = adPath.split('/');
    let containerName = stubs[stubs.length - 2];

    containerName = `${containerName}-${env}`;

    // re assign the position to make life easier
    stubs[stubs.length - 2] = containerName;

    adPath = stubs.join('/');
  }

  // This is an opportunity to cache the ad itself to reduce hits on AEM
  try {
    response = await brandClient.get(`${adPath}`);
    // Is this something we want to support?  Forced absolute paths?
    retVal = convertRelativeToAbsolute(response.data, `https://www.${brand.toLowerCase()}.com`);
  } catch (err) {
    return new Error(`Failed to make call to AEM to fetch Ad Markup ${err}`);
  }

  // Get tokens from the ad's config, they are optional
  if (bestMatch?.tokenizedData?.value) {
    try {
      adTokenConfig = JSON.parse(bestMatch.tokenizedData.value);
      logDecision(`AdTokenConfig: ${adTokenConfig}`);
    } catch (err) {
      console.error(`Error parsing ad Tokens ${err}`);
      logDecision(`AdTokenConfigParseError: ${err}`);
    }
  }

  // We need to replace %%TOKENS%% in our ad
  if (adTokenConfig || templateTokensOverrides) {
    retVal = replaceTokens(retVal, adTokenConfig, templateTokensOverrides);
  }
  return retVal;
  // console.log(`RETURN THIS AD ${JSON.stringify(retVal, null, '\t')}`)
}

/**
 * This functions converts all the relative URLs to absolute in the ad's HTML
 * @param html
 * @param hostname
 * @returns
 *
 */
function convertRelativeToAbsolute(html: string, hostname: string): string {
  const $ = cheerio.load(html);
  // Helper-function to determine absolute path
  function isAbsoluteUrl(url: string) {
    const regexp = /^(?:[a-z]+:)?\/\//i;
    return regexp.test(url);
  }

  // Collect <link> and <script> tags
  const linkAndScriptTags = $('link, script');

  linkAndScriptTags.each((_, element) => {
    const tagName = $(element).prop('tagName')?.toLowerCase();
    const attrName = tagName === 'link' ? 'href' : 'src';
    const url = $(element).attr(attrName);

    // modify the URL if it exists and it's a relative path
    if (url && !isAbsoluteUrl(url)) {
      $(element).attr(attrName, `${hostname}${url}`);
    }
  });

  return $.html();
}

// Search inside adContent for any tokens we are being asked to replace
// and swap them out with the provided value
function replaceTokens(content: any, adTokenConfig: any, templateTokensOverrides: any) {
  let updatedContent = content;

  // First process any overrides that could have come from the requestor
  // These trump any from the adConfig
  if (templateTokensOverrides.length) {
    for (let token of templateTokensOverrides) {
      const tokenData = token.token;
      if (tokenData?.tokenName && tokenData?.tokenValue) {
        const regex = new RegExp(tokenData.tokenName, 'g');
        updatedContent = updatedContent.replace(regex, tokenData.tokenValue);
      }
    }
  }

  if (adTokenConfig) {
    for (let token of adTokenConfig) {
      const tokenData = token.token;
      if (tokenData?.tokenName && tokenData?.tokenValue) {
        const regex = new RegExp(tokenData.tokenName, 'g');
        updatedContent = updatedContent.replace(regex, tokenData.tokenValue);
      }
    }
  }
  return updatedContent;
}

export function getAdProperties(
  props: Entity['properties'],
  logDecision: tLogDecision,
): [string[], number] {
  let adTags: string[] = [];
  let adPriority: number = 0;

  // If exists and array, grab tags
  if (props.elements?.tags?.value && props.elements?.tags?.multiValue) {
    adTags = props.elements.tags.value;
    logDecision(`AdTags: ${adTags}`);
  }

  // If exists and array, grab priority
  if (props.elements?.priority?.value) {
    adPriority = props.elements.priority.value;
    logDecision(`adPriority: ${adPriority}`);
  }

  return [adTags, adPriority];
}

export function matchHostname(hostname: string, pattern: string) {
  // Split the pattern into sections divided by *
  const regexParts = pattern.split('*');

  // Map over the sections, returning the correct regex for each section
  const regexString = regexParts
    .map((part, idx) => {
      if (idx === 0 || idx === regexParts.length - 1) {
        // If it's the start or end of the pattern, make the regex
        // match anything other than a '/'
        return part.replace(/\*/g, '[^/]*');
      } else {
        // If it's in the middle of the pattern, make the regex
        // match absolutely anything
        return part.replace(/\*/g, '.*');
      }
    })
    .join('.*'); // Combine the regex sections

  const regex = new RegExp(regexString);
  // Test if the referer path matches the pattern
  return regex.test(hostname);
}
