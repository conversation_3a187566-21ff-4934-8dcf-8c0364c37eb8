import { AMHP<PERSON>ards, AMWI, EVENT_TYPES, InCart, PROD } from 'app/constants';
import { Env, iRule, tBrand } from 'app/models/aemRules';
import { isAlternateDomain, isGoogleWorkspace, isPrivateReg } from '../productIdentifiers';
import { CONFIGS } from 'app/cache/configCache';
import {
  AdObj,
  AdResponse,
  fgProductLite,
  GwsDomain,
  iCacheItem,
  iUsers,
  XSellRequestBody,
  configEnv,
} from 'app/models';
import { getSiteId } from '..';
import { getUser } from 'app/services/userAccountQueryService';
import { sfLogger } from 'app/logger';
import fetchAlternateDomain from '../fetchAlternateDomain';
import { getConfigEnv } from 'app/helpers';

const getAdsFromRules = (
  rules: iRule[],
  xSellRequestBody: XSellRequestBody,
  domainsEligibleForGWS: GwsDomain[],
  rngTrace: string,
  allDomains: fgProductLite[],
) => {
  const { containerName, brand, testOffers, userId = undefined } = xSellRequestBody;
  const isTestOffers = testOffers && testOffers.length > 0;
  const currencyCode =
    xSellRequestBody?.cart?.currencyCode || xSellRequestBody?.currencyCode || 'USD';

  let { env } = xSellRequestBody;

  // translate to correct env settings
  let configInfo: configEnv = getConfigEnv(env);
  const configEnv: string = configInfo.configEnv;
  env = configInfo.env.toLowerCase() as Env;

  const promises = rules.map(async (rule) => {
    const htmlPath =
      env === PROD ? rule?.fragmentDetails?.htmlPath : rule?.fragmentDetails?.htmlPathQA;

    const isGwsAd = isGoogleWorkspace(rule?.fragmentDetails?.sku);
    const isPriRegAd = isPrivateReg(rule?.fragmentDetails?.sku);

    // Not returning the ad if the ad path is not available.
    if (!htmlPath) return;

    // Not returning the ad if there are no eligible domains for networksolutions.
    if (
      containerName === InCart &&
      isGwsAd &&
      brand === 'NETWORKSOLUTIONS' &&
      domainsEligibleForGWS.length === 0
    ) {
      return;
    }

    const productMarkup = await CONFIGS[env][brand as tBrand].getAd(
      `${rule.brand}-${rule.containerName}-${currencyCode}-${htmlPath}`,
    );

    // We don't want to return other details if productMarkup is not available.
    if (!productMarkup) {
      return;
    }
    let productMarkupCopy: iCacheItem | undefined = undefined;

    // make a copy so we dont affect the cache
    if (productMarkup) {
      productMarkupCopy = {
        htmlString: productMarkup.htmlString,
        headContent: productMarkup.headContent,
        bodyContent: productMarkup.bodyContent,
        linkTags: productMarkup.linkTags,
        scriptTags: productMarkup.scriptTags,
      };
    }

    if (isPriRegAd && productMarkupCopy && productMarkupCopy.bodyContent && userId) {
      let users: iUsers[] = [];
      users = await getUser(env, userId);

      if (users) {
        const user = users[0].user;
        const addressObj = user.addresses[0];

        const name = user.firstName + ' ' + user.lastName;
        const phone = user.phone.phoneNumber;
        const addressL1 = addressObj?.addressLine1;
        const addressL2 = addressObj?.addressLine2;
        const city = addressObj?.city;
        const state = addressObj?.state;
        const email = user?.email;
        const postalCode = addressObj?.postalCode;

        productMarkupCopy.bodyContent = productMarkupCopy.bodyContent.replace(/__AD_NAME__/g, name);
        productMarkupCopy.bodyContent = productMarkupCopy.bodyContent.replace(
          /__AD_ADDRESS_L1__/g,
          addressL1,
        );
        if (addressL2) {
          productMarkupCopy.bodyContent = productMarkupCopy.bodyContent.replace(
            /__AD_ADDRESS_L2__/g,
            addressL2,
          );
        }
        productMarkupCopy.bodyContent = productMarkupCopy.bodyContent.replace(/__AD_CITY__/g, city);
        productMarkupCopy.bodyContent = productMarkupCopy.bodyContent.replace(
          /__AD_STATE__/g,
          state,
        );
        productMarkupCopy.bodyContent = productMarkupCopy.bodyContent.replace(
          /__AD_POSTALCODE__/g,
          postalCode,
        );
        productMarkupCopy.bodyContent = productMarkupCopy.bodyContent.replace(
          /__AD_PHONE__/g,
          phone,
        );
        productMarkupCopy.bodyContent = productMarkupCopy.bodyContent.replace(
          /__AD_EMAIL__/g,
          email,
        );
      }
    }

    // Replacing placeholder with example email for GWS
    if (isGwsAd && domainsEligibleForGWS.length && productMarkupCopy) {
      const firstDomainFromCart = domainsEligibleForGWS[0].prodInstName;
      if (firstDomainFromCart && productMarkupCopy.bodyContent?.match(/__AD_EMAIL__/g)) {
        let emailString = '';
        let users: iUsers[] = [];
        if (userId) {
          try {
            // If userId is available, fetch the user details to get the first name
            users = await getUser(env, userId);
          } catch (err) {
            // Handle error if user details cannot be fetched
            console.error('Error fetching user details:', err);
            sfLogger.error(
              {
                eventType: EVENT_TYPES.QueryUser_Fail,
                message: 'Error fetching user details for personalized email',
                errorDetails: (err as Error).message,
                customAttributes: {
                  brand,
                  userId,
                  env,
                },
              },
              rngTrace,
            );
          }
          const firstName = users?.[0]?.user.firstName?.toLowerCase() || 'yourname';
          emailString = firstName + `@${firstDomainFromCart.toLowerCase()}`;
        } else {
          // Or we will use a default name
          emailString = `yourname@${firstDomainFromCart.toLowerCase()}`;
        }
        productMarkupCopy.bodyContent = productMarkupCopy?.bodyContent.replace(
          /__AD_EMAIL__/g,
          emailString,
        );
      }
    }

    // Replacing placeholder with Alternate Domain for Alternate Domain Ad
    let alternateDomains = [];

    if (
      allDomains.length > 0 &&
      isAlternateDomain(rule?.fragmentDetails?.sku) &&
      productMarkupCopy?.bodyContent?.match(/__AD_ALTDOMAIN__/g)
    ) {
      alternateDomains = await fetchAlternateDomain(brand as tBrand, env, allDomains[0]);

      if (!alternateDomains || !alternateDomains.length) {
        // If the alternate domain is not found, we won't show the ad.
        return;
      }
      productMarkupCopy.bodyContent = productMarkupCopy?.bodyContent.replace(
        /__AD_ALTDOMAIN__/g,
        alternateDomains[0].domainName,
      );
    }

    if (productMarkupCopy) {
      // Due to email sent from AEM as [email protected], we need to send a placeholder with @ and then replace it
      productMarkupCopy.bodyContent = productMarkupCopy.bodyContent.replaceAll(/@_AD_EMAIL_/g, '@');
    }

    if (alternateDomains?.length > 0)
      alternateDomains = alternateDomains.map((domain: any) => ({
        domainName: domain.domainName,
        availabilityStatus: domain.available,
        price: domain.unitPrice,
        productCode: domain.sku,
        unitPrice: domain.renewPrice,
      }));
    let response: AdObj = {
      productSku: rule?.fragmentDetails?.adSku,
      sku: rule?.fragmentDetails?.sku,
      adType: rule?.fragmentDetails?.adType,
      productMarkup: productMarkupCopy,
      priority: rule?.priority,
      weightage: rule.weightage,
      // ...(rule.weightage && { weightage }),
      actionType: rule?.actionType,
      pricingTerm: rule?.pricingTerm,
      landingCode: rule?.landingCode,
      channelID: rule?.channelID,
      coupon: rule?.coupon,
      adIdentifier: rule?.businessRule,
      alias: rule?.alias,
      ...(rule?.checkDomainsEligibleGWS &&
        !isTestOffers && {
          eligibleDomainsList: domainsEligibleForGWS,
        }),
      ...(rule?.packageEligible && {
        packageEligible: rule?.packageEligible,
        packageName: rule?.fragmentDetails.packageName,
      }),
      ...(alternateDomains?.length > 0 && {
        requestedDomain: allDomains[0]?.prodInstName,
        alternateDomains,
      }),
    };

    if (containerName === InCart) {
      response = {
        ...response,
        disclaimer: rule?.disclaimerCopy,
        siteId: rule?.pricingSiteId?.toString() || getSiteId(brand as tBrand),
      };
    } else if (containerName === AMWI || containerName === AMHPCards) {
      response = {
        ...response,
        redirect: rule?.redirect,
      };
    }
    return response;
  });
  return promises;
};
export default getAdsFromRules;
