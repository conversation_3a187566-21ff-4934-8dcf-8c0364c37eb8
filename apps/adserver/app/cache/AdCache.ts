import { iCacheItem } from 'app/models';
import { BRAND_MAPPER, Cache } from './Cache';
import { Env, iAemRules, iRule, tBrand } from 'app/models/aemRules';
import axios from 'axios';
import { sfLogger } from 'app/logger';
import { ERROR_MESSAGES, PROD, SFLOGGER_IDS } from 'app/constants';
import { injectPriceInAd } from 'app/helpers/injectPriceInAd';
import { iPricingBffTerm } from 'app/helpers/pricingBffHelper';
import { parseHtml, transformAd } from 'app/helpers/cheerioHelper';

const SURFACES = ['uppcart', 'amwi', 'amhp'];

const BRAND_CURRENCIES = [
  {
    brand: 'NETWORKSOLUTIONS',
    currencies: ['USD', 'GBP'],
  },
  {
    brand: 'HOSTGATOR',
    currencies: ['USD'],
  },
  {
    brand: 'BLUEHOST',
    currencies: ['USD', 'GBP', 'EUR', 'CAD', 'AUD', 'INR'],
  },
];

export class AdCache extends Cache {
  constructor(brand: tBrand, env: Env) {
    super(brand, env);
  }

  public statusMessages: string[] = [];

  protected async fetchConfig(): Promise<iAemRules> {
    /**
     * We replace the BFF URL with this as we don't want to deal with CF cache
     * The BFF calls this URL internally
     */
    const allRules: iRule[] = [];

    // Fetch config for each surface
    await Promise.all(
      SURFACES.map(async (surface) => {
        try {
          const response = await axios.get(
            `https://sfbff.newfold.com/getContent/json/assets/adserver-ads/${surface}/${
              BRAND_MAPPER[this.brand]
            }-${surface}-ads.json`,
          );

          const surfaceConfig = JSON.parse(response.data.attributes);

          // Filter out rules with weightage <= 0 (We are using this as a flag to disable ads)
          const rules = surfaceConfig.rules.filter(
            (rule: iRule) => rule.weightage === undefined || rule.weightage > 0,
          );

          // Add surface-specific rules to the array
          allRules.push(...rules);
        } catch (e: any) {
          const errorMessage = `Failed to fetch config for surface: ${BRAND_MAPPER[this.brand]}-${surface}`;
          sfLogger.error(
            {
              eventType: 'ADAM_Config_CE_3',
              message: errorMessage,
              errorDetails: e.message,
              customAttributes: {
                initiator: SFLOGGER_IDS.SFLOG_ID_01,
                brand: this.brand,
              },
            },
            '',
          );
          this.statusMessages.push(errorMessage + ` : ${e.message}`);
          console.error(errorMessage, e);
        }
      }),
    );

    return { rules: allRules };
  }

  protected async setConfig(): Promise<void> {
    try {
      this.clearStatusMessages();
      const newConfig = await this.fetchConfig();
      const newAdCache: Record<string, iCacheItem> = {};

      let currencies: string[] = [];
      for (let j = 0; j < BRAND_CURRENCIES.length; j++) {
        if (BRAND_CURRENCIES[j].brand === this.brand) {
          currencies = BRAND_CURRENCIES[j].currencies;
          break;
        }
      }

      if (!currencies.length || currencies.length === 0) {
        throw new Error(`The currencies are not found for brand ${this.brand}`);
      }

      for (let i = 0; i < currencies.length; i++) {
        const currencyCode = currencies[i];
        // Pre-fetch all ads to build a new ad cache
        await Promise.all(
          newConfig.rules.flatMap(async (rule) => {
            const htmlPath =
              this.env === PROD
                ? rule?.fragmentDetails?.htmlPath
                : rule?.fragmentDetails?.htmlPathQA;
            if (!htmlPath) return;
            const ad = await this.fetchAd(htmlPath);
            if (!ad) return;

            const refinedAd: string = await injectPriceInAd(
              ad,
              rule.pricingTerm,
              rule.priceDisplayTerm as iPricingBffTerm,
              rule.coupon,
              rule.fragmentDetails.adSku,
              this.brand,
              currencyCode,
              rule?.pricingSiteId,
              this.env,
              this.statusMessages,
              rule.containerName,
            );

            const adKey = `${rule.brand}-${rule.containerName}-${currencyCode}-${htmlPath}`;
            newAdCache[adKey] = parseHtml(refinedAd);

            if (rule.fragmentDetails?.defaultSelectedPlan) {
              newAdCache[adKey].bodyContent = transformAd(rule, refinedAd);
            }
          }),
        );
      }

      /**
       * Atomically replace the config and adCache
       * Yes, this is an atomic operation so we don't need
       * a mutex or anything like that
       */
      this.config = newConfig;
      this.adCache = newAdCache;
      this.isCacheEmpty = false;
    } catch (e: any) {
      if (this.env === 'prod') {
        sfLogger.error(
          {
            eventType: 'ADAM_Config_CE_2',
            message: ERROR_MESSAGES.ConfigUpdateFailed,
            errorDetails: e.message,
            customAttributes: {
              initiator: SFLOGGER_IDS.SFLOG_ID_03,
              brand: this.brand,
              env: this.env,
            },
          },
          '',
        );
      }
      const errorMessage = `${ERROR_MESSAGES.ConfigUpdateFailed} for brand ${this.brand} for env ${this.env}`;
      this.statusMessages.push(errorMessage + `: ${e.message}`);
      console.error(errorMessage, e);
    }
  }

  public clearStatusMessages() {
    if (this.statusMessages && this.statusMessages.length > 0) {
      this.statusMessages = [];
    }
  }
}
