import { getDisplayTerm } from 'app/helpers/injectPriceInAd';
import { getPriceKey, getRawProductPrice } from 'app/helpers/pricingBffHelper';
import { NewCartAd, NewCartAdsData, RawProductPrice } from 'app/models';
import { Env } from 'app/models/aemRules';
import { getSiteId } from 'app/helpers/';
import axios from 'axios';

const BRAND_CURRENCIES = [
  {
    brand: 'BLUEHOST',
    currencies: ['USD', 'GBP', 'EUR', 'CAD', 'AUD', 'INR'],
  },
];

export class NewCartCache {
  private adCache: Record<string, NewCartAd>;
  private env: Env;
  protected async setConfig(): Promise<void> {
    let allCurrencies: string[] = [];
    for (let j = 0; j < BRAND_CURRENCIES.length; j++) {
      allCurrencies = BRAND_CURRENCIES[j].currencies;
    }
    const response = await axios.get(
      'https://sfbff.newfold.com/getContent/json/assets/adserver-ads/newcart/bh-newcart-ads.json',
    );
    const rules = JSON.parse(response.data.attributes);

    for (let i = 0; i < rules.rules.length; i++) {
      const ad = rules.rules[i];

      for (let j = 0; j < allCurrencies.length; j++) {
        const currency = allCurrencies[j];

        let productPrice: RawProductPrice = await getRawProductPrice(
          ad.adSku,
          'BLUEHOST',
          ad.coupon || '',
          currency,
          ad.pricingSiteId,
          this.env,
        );
        let priceToDisplay1: string = '';
        let priceToDisplay2: string = '';

        if (productPrice?.terms.length > 0) {
          // Getting required pricing term from all terms
          const priceObj = productPrice.terms.find(
            (i: any) => i.termUnitDescription === ad.pricingTerm && i.termQuantity === 1,
          );

          let pricingText1: string =
            productPrice.currencySymbol + priceObj?.[getPriceKey(ad.priceDisplayTerm1)]; // Required price for the ad from pricing obj
          pricingText1 += getDisplayTerm(ad.priceDisplayTerm1); // Adding /mo or /yr in the end

          let pricingText2: string =
            productPrice.currencySymbol + priceObj?.[getPriceKey(ad.priceDisplayTerm2, true)]; // Required price for the ad from pricing obj
          pricingText2 += getDisplayTerm(ad.priceDisplayTerm2); // Adding /mo or /yr in the end

          priceToDisplay1 = ad.pricingText1.replace('__PRICE_AD__', pricingText1);
          priceToDisplay2 = ad.pricingText2.replace('__PRICE_AD__', pricingText2);
        }

        const finalAdObj = {
          name: ad.name,
          pricingText1: priceToDisplay1,
          adSku: ad.adSku,
          sku: ad.sku,
          pricingText2: priceToDisplay2,
          siteId: ad?.pricingSiteId?.toString() || getSiteId('BLUEHOST'),
          landingCode: ad.landingCode,
          modalDetails: ad?.modalDetails,
          coupon: ad?.coupon,
          channelId: 263,
        };

        this.adCache[`${this.env}-${currency}-${ad.sku}`] = finalAdObj;
      }
    }
  }

  public getAd(currency: string, sku: string) {
    return this.adCache[`${this.env}-${currency}-${sku}`];
  }

  public flushCache() {
    this.setConfig();
  }

  constructor(env: Env) {
    this.adCache = {};
    this.env = env;
    // Fetch the initial config
    this.setConfig();

    // Periodically update the config in the background
    setInterval(() => {
      this.setConfig().catch((e) =>
        console.error('Periodic config update failed for new cart:', e),
      );
    }, 1000 * 600);
  }
}
