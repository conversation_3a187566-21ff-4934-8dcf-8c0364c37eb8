import { Env, tBrand, ConfigCacheResults, iAemRules } from 'app/models/aemRules';
import { AdCache } from './AdCache';
import { PROD, QA } from 'app/constants';
import { ExpFragCache } from './ExpFragCache';
import { NewCartCache } from './NewCartCache';

export const CONFIGS: Record<Env, Record<tBrand, AdCache>> = {
  qa: {
    BLUEHOST: new AdCache('BLUEHOST', QA),
    HOSTGATOR: new AdCache('HOSTGATOR', QA),
    NETWORKSOLUTIONS: new AdCache('NETWORKSOLUTIONS', QA),
  },

  prod: {
    BLUEHOST: new AdCache('BLUEHOST', PROD),
    HOSTGATOR: new AdCache('HOSTGATOR', PROD),
    NETWORKSOLUTIONS: new AdCache('NETWORKSOLUTIONS', PROD),
  },
};

export const EXP_FRAG_CONFIGS: Record<Env, Partial<Record<tBrand, ExpFragCache>>> = {
  qa: {
    NETWORKSOLUTIONS: new ExpFragCache('NETWORKSOLUTIONS', QA),
    BLUEHOST: new ExpFragCache('BLUEHOST', QA),
  },
  prod: {
    NETWORKSOLUTIONS: new ExpFragCache('NETWORKSOLUTIONS', PROD),
    BLUEHOST: new ExpFragCache('BLUEHOST', PROD),
  },
};

export const NEW_CART_CACHE = { qa: new NewCartCache('qa'), prod: new NewCartCache('prod') }; // No brands as we are doing this for BH only for now

export async function getConfigs(env: Env, brand: tBrand): Promise<ConfigCacheResults> {
  let AEM_RULES: iAemRules | undefined = await CONFIGS[env][brand as tBrand].getConfig();

  let configResult: ConfigCacheResults = {
    configs: AEM_RULES,
    statusMessages: CONFIGS[env][brand as tBrand].statusMessages,
  };

  return configResult;
}

export function flushCache() {
  console.log('cache flush initiated');
  (['BLUEHOST', 'HOSTGATOR', 'NETWORKSOLUTIONS'] as tBrand[]).forEach((brand) => {
    CONFIGS[PROD][brand]?.flushCache();
    CONFIGS[QA][brand]?.flushCache();
  });
  (['BLUEHOST', 'NETWORKSOLUTIONS'] as tBrand[]).forEach((brand) => {
    EXP_FRAG_CONFIGS[PROD][brand]?.flushCache();
    EXP_FRAG_CONFIGS[QA][brand]?.flushCache();
  });
  NEW_CART_CACHE[PROD]?.flushCache();
  NEW_CART_CACHE[QA]?.flushCache();
}

export function isCacheFilled() {
  const ready = (['BLUEHOST', 'HOSTGATOR', 'NETWORKSOLUTIONS'] as tBrand[])
    .map(
      (brand) =>
        CONFIGS[PROD][brand]?.isCacheEmpty ||
        CONFIGS[QA][brand]?.isCacheEmpty ||
        EXP_FRAG_CONFIGS[PROD][brand]?.isCacheEmpty ||
        EXP_FRAG_CONFIGS[QA][brand]?.isCacheEmpty,
    )
    .every((item) => item === false);

  return ready;
}
