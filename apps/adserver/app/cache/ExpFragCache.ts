import { iCacheItem } from 'app/models';
import { BRAND_MAPPER, Cache } from './Cache';
import { Env, iAemRules, iRule, tBrand } from 'app/models/aemRules';
import axios from 'axios';
import { sfLogger } from 'app/logger';
import { ERROR_MESSAGES, PROD, SFLOGGER_IDS } from 'app/constants';
import { parseHtml } from 'app/helpers/cheerioHelper';

export class ExpFragCache extends Cache {
  constructor(brand: tBrand, env: Env) {
    super(brand, env);
  }

  protected async fetchConfig(): Promise<iAemRules> {
    /**
     * We replace the BFF URl with this as we dont want to deal with CF cache
     * The BFF calls this URL internally
     */
    const allRules: iRule[] = [];

    // Fetch config for each surface
    try {
      const response = await axios.get(
        `https://sfbff.newfold.com/getContent/json/assets/adserver-ads/expfrag/${
          BRAND_MAPPER[this.brand]
        }-expfrag.json`,
      );
      const surfaceConfig = JSON.parse(response.data.attributes);

      // Add surface-specific rules to the array
      allRules.push(...surfaceConfig.rules);
    } catch (e: any) {
      sfLogger.error(
        {
          eventType: 'ADAM_Config_CE_3',
          message: `Failed to fetch config for surface: expfrag-${this.brand}`,
          errorDetails: e.message,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_01,
            brand: this.brand,
          },
        },
        '',
      );
      console.error(`Failed to fetch config for surface: ${BRAND_MAPPER[this.brand]}-expfrag`, e);
    }

    return { rules: allRules };
  }

  protected async setConfig(): Promise<void> {
    try {
      const newConfig = await this.fetchConfig();
      const newAdCache: Record<string, iCacheItem> = {};
      const expFrags = newConfig.rules.filter((rule) => rule.containerName === 'blogs');

      // Add expFrags to the adCache
      for (let i = 0; i < expFrags.length; i++) {
        const rule = expFrags[i];

        const htmlPath =
          this.env === PROD ? rule?.fragmentDetails?.htmlPath : rule?.fragmentDetails?.htmlPathQA;
        if (!htmlPath) return;
        const ad = await this.fetchAd(htmlPath);
        if (!ad) {
          return;
        }
        const parsedHtml = parseHtml(ad);
        delete parsedHtml.htmlString;
        delete parsedHtml.headContent;

        // Removing all <script> tags containing "CQ_Analytics" from bodyContent as netsol team wants
        parsedHtml.bodyContent = parsedHtml.bodyContent.replace(
          /<script\b[^>]*>[\s\S]*?CQ_Analytics[\s\S]*?<\/script>/gi,
          '',
        );
        parsedHtml.bodyContent = parsedHtml.bodyContent.replace(
          /<script\b[^>]*src=["'][^"']*clientlib-header\.min\.[^"']*\.js[^"']*["'][^>]*>[\s\S]*?<\/script>/gi,
          '',
        );

        parsedHtml.bodyContent = parsedHtml.bodyContent.replace(
          /<script\b[^>]*src=["'][^"']*clientlib-footer\.min\.[^"']*\.js[^"']*["'][^>]*>[\s\S]*?<\/script>/gi,
          '',
        );

        parsedHtml.bodyContent = parsedHtml.bodyContent.replace(
          /<script\b[^>]*src=["'][^"']*site\.min\.[^"']*\.js[^"']*["'][^>]*>[\s\S]*?<\/script>/gi,
          '',
        );

        parsedHtml.bodyContent = parsedHtml.bodyContent.replace(
          /<script>\s*\(function\(\)\{[\s\S]*?window\.__CF\$cv\$params[\s\S]*?\}\}\)\(\);\s*<\/script>/gi,
          '',
        );

        // Removing script tag that has testandtarget path in src
        parsedHtml.scriptTags = parsedHtml.scriptTags.filter((script) => {
          return !script.src?.includes('/etc.clientlibs/cq/testandtarget/clientlibs/testandtarget');
        });
        // Removing unwanted link tag
        parsedHtml.linkTags = parsedHtml.linkTags.filter((link) => {
          return !link.href?.includes(
            '/content/dam/netsol/theme-assets/dc-netsol-theme-********.css',
          );
        });
        newAdCache[`${rule.brand}-${rule.containerName}-${htmlPath}`] = parsedHtml;
      }

      /**
       * Atomically replace the config and adCache
       * Yes, this is an atomic operation so we don't need
       * a mutex or anything like that
       */
      this.config = newConfig;
      this.adCache = newAdCache;
      this.isCacheEmpty = false;
    } catch (e: any) {
      if (this.env === 'prod') {
        sfLogger.error(
          {
            eventType: 'ADAM_Config_CE_2',
            message: ERROR_MESSAGES.ConfigUpdateFailed,
            errorDetails: e.message,
            customAttributes: {
              initiator: SFLOGGER_IDS.SFLOG_ID_03,
              brand: this.brand,
              env: this.env,
            },
          },
          '',
        );
      }
      console.error(ERROR_MESSAGES.ConfigUpdateFailed, e);
    }
  }
}
