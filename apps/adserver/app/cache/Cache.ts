import { PROD, SFLOGGER_IDS } from 'app/constants';
import { sfLogger } from 'app/logger';
import { iCacheItem } from 'app/models';
import { iAemRules, Env, tBrand, iRule } from 'app/models/aemRules';
import AdServerService from 'app/services/adServerService';

const adServerService = new AdServerService();

export const BRAND_MAPPER: Record<tBrand, string> = {
  NETWORKSOLUTIONS: 'netsol',
  HOSTGATOR: 'hg',
  BLUEHOST: 'bh',
};

// Base class for caching Ads and Experience fragments.
export abstract class Cache {
  protected config?: iAemRules;
  protected adCache: Record<string, iCacheItem>;
  protected brand: tBrand;
  protected env: Env = PROD;
  public isCacheEmpty: boolean = true;

  public async getAd(path: string): Promise<iCacheItem | undefined> {
    return this.adCache[path];
  }

  protected abstract fetchConfig(): Promise<iAemRules>;
  protected abstract setConfig(): Promise<void>;

  protected async fetchAd(path: string): Promise<string | undefined> {
    let retVal;
    if (!path) return '';
    
    try {
      const response = await adServerService.getAd({
        adPath: path,
        brand: this.brand,
        tags: '',
        fetchLogic: false,
        env: '',
        templateTokensOverrides: [],
        useDisabledAds: false,
      });
      // const response = await axios.get(`https://sfbff.newfold.com/adServer/getAd?adPath=${path}`);
      // const response = await axios.get(`http://localhost:8080/adServer/getAd?adPath=${path}`);
      // let response;
      // if (this.env === PROD) {
      //   response = await axios.get(`https://sfbff.newfold.com/adServer/getAd?adPath=${path}`);
      // } else {
      //   response = await axios.get(path);
      // }
      retVal = response;
    } catch (e: any) {
      if (this.env === 'prod') {
        // we only want the prod logs
        sfLogger.error(
          {
            eventType: 'ADAM_Config_CE_1',
            message: `Ad ${path} does not exist`,
            errorDetails: e.message,
            customAttributes: {
              initiator: SFLOGGER_IDS.SFLOG_ID_02,
              brand: this.brand,
              env: this.env,
            },
          },
          '',
        );
      }
      console.error(`Env: ${this.env} Ad ${path} does not exist`);
    }

    return retVal;
  }

  public async getConfig(): Promise<iAemRules | undefined> {
    return this.config;
  }

  public async flushCache() {
    this.setConfig();
  }

  constructor(brand: tBrand, env: Env) {
    this.adCache = {};
    this.brand = brand;
    this.env = env.toLowerCase() as Env;

    // Fetch the initial config
    this.setConfig();

    // Periodically update the config in the background
    setInterval(() => {
      this.setConfig().catch((e) => console.error('Periodic config update failed:', e));
    }, 1000 * 600);
  }
}
