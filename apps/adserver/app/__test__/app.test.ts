import app from '../index';
import request from 'supertest';

beforeAll(() => {
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

describe('app', () => {
  it('1. should return 200 for GET /', async () => {
    const response = await request(app).get('/');
    expect(response.status).toBe(200);
  });

  it('2. should return 200 for GET /adServer/status', async () => {
    const res = await request(app).get('/adServer/status');
    expect(res.status).toBe(200);
  });

  it('3. should return 200 for GET /maintenance', async () => {
    const res = await request(app).get('/maintenance');
    expect(res.status).toBe(200);
  });
});
  
