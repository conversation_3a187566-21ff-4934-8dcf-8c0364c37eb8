import axios from 'axios';
import { getConfigForEnv } from '../helpers/configHelper';
import { environmentConfig } from 'app/models';

/**
 * query the sites existing for the hosting package
 */
export async function querySitesForHosting(
  currentEnv: string,
  token: string,
  productInstanceId: string,
) {
  let isSuccess = false;
  let responseDataOutput = null;
  let errCode = null;
  let errMessage = null;
  let errMessageCode = null;

  try {
    const configFile: environmentConfig | undefined = getConfigForEnv(currentEnv);

    if (configFile === undefined) {
      throw new Error(
        `hostingInfoFetcher:querySitesForHosting() - configFile is undefined for env=${currentEnv}`,
      );
    }

    const baseURL = configFile.hostingApi;
    const timeout = configFile.hostingApiTimeout;

    let fullUrl = baseURL + `/v2/hosting/${productInstanceId}/sites`;

    let headers = {};

    headers = {
      Authorization: `Bearer ${token}`,
    };

    const responseData = await axios.get(fullUrl, {
      headers,
      timeout: timeout,
    });

    isSuccess = true;
    responseDataOutput = responseData.data;

    console.log(`${fullUrl} response ok`);
  } catch (err: any) {
    if (err.response && err.response.status) {
      errCode = err.response.status;
      if (err.response.data.message) {
        errMessage = err.response.data.message;
      } else if (err.response.data.errorMessage) {
        errMessage = err.response.data.errorMessage;
      }

      if (errMessage && err.response.data.code) {
        errMessageCode = err.response.data.code;
        errMessage = `${errMessage}, code = ${errMessageCode}`;
      }
      const finalErrMsg = `hostingInfoFetcher.querySitesForHosting - jarvis error was ${errMessage}`;
      console.error(finalErrMsg);
      err;
    } else if (err.message) {
      errCode = 500;
      errMessage = err.message;
      const finalErrMsg = `hostingInfoFetcher.querySitesForHosting - jarvis error was ${errMessage}`;
      console.error(finalErrMsg);
      throw err;
    }
    throw err;
  }

  /* package the response in a consistent way */
  const customerData = {
    status: isSuccess ? 'success' : 'error',
    errorCode: errCode,
    data: responseDataOutput,
    errMessage,
  };
  return customerData;
}
