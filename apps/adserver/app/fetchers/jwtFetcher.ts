
import axios from 'axios';
import { getConfigForEnv } from '../helpers/configHelper';
import { environmentConfig } from 'app/models';
import { getParam } from './paramFetcher';

export function getSubjectStr(accountId: string | undefined, userId: string | undefined, brand: string) {
  
  let subjectStr = "";

  let brandStr = brand.toLowerCase();

  if (accountId) {
    const numAccountId = Number(accountId);
    if(numAccountId > 0) {
        subjectStr = subjectStr + ":" + brandStr + ":" + "account:" + accountId;
    } else {
      subjectStr = subjectStr + ":" + brandStr + ":" + "user:" + userId;
    }
  } else {
    subjectStr = subjectStr + ":" + brandStr + ":" + "user:" + userId;
  }
  return subjectStr;
}

export function getActStr(accountId: string | undefined, userId: string | undefined, brand: string) {
  
  let subjectStr = "";

  let brandStr = brand.toLowerCase();

  if (accountId) {
    const numAccountId = Number(accountId);
    if(numAccountId > 0) {
        //subjectStr = subjectStr + ":" + brandStr + ":" + "account:" + accountId;
        subjectStr = subjectStr + ":" + brandStr + ":" + "user:" + accountId;
    } else {
      subjectStr = subjectStr + ":" + brandStr + ":" + "user:" + userId;
    }
  } else {
    subjectStr = subjectStr + ":" + brandStr + ":" + "user:" + userId;
  }
  return subjectStr;
}

export async function fetchJwtToken(currentEnv: string, brand: string, userId: string | undefined, accountId: string | undefined) : Promise<string> {
  let responseData = null;
  let errMessageCode = null;
  let token: string;
  let errMessage = null;

  try {
    const configFile: environmentConfig | undefined = getConfigForEnv(currentEnv);

    if (configFile === undefined) {
      throw new Error(
        `jwtFetcher:fetchJwtToken() - configFile is undefined for env=${currentEnv}`,
      );
    }

    const baseURL = configFile.jwtJarvisTokenUrl;
    console.log(`baseUrl = ${baseURL}`);
    const timeout = configFile.jwtApiTimeout;

    const axiosJarvisFG = axios.create({
      baseURL,
      timeout,
    });

    const subjectStr = "urn:jarvis" + getSubjectStr(accountId, userId, brand);
    const actStr = "urn:jarvis" + getActStr(accountId, userId, brand);

    const clientId = getParam(currentEnv, 'jwtJarvisClientId');

    const data = {
      "issuer": "jarvis-jwt",
      "subject": `${subjectStr}`,
      "act": {
        "sub": `${actStr}`,
        "role": "admin",
      },
      "scope": "user-fe",
      "clientId": `${clientId}`,
      "clientName": `${configFile.jwtJarvisClientName}`,
      "expirationTime": `${configFile.jwtJarvisTokenExpirationTime}`,
    };

    const headers = {
      'Content-Type': 'application/json',
    };

    console.log("calling /auth/generateUJwtTokenV1");

    responseData = await axiosJarvisFG.post('/auth/generateUJwtTokenV1', data, {
      headers
    });
    console.log(`/auth/generateUJwtTokenV1 response ok`);
    token = responseData.data.token;

  } catch (err: any) {
    if (err.response && err.response.status) {
        if (err.response.data.message) {
            errMessage = err.response.data.message;
        } else if (err.response.data.errorMessage) {
            errMessage = err.response.data.errorMessage;
        }

        if (errMessage && err.response.data.code) {
            errMessageCode = err.response.data.code;
            errMessage = `${errMessage}, code = ${errMessageCode}`;
        }
        const finalErrMsg = `jwtFetcher.fetchJwtToken - jarvis error was ${errMessage}`;
        console.error(finalErrMsg);
        throw err;
    } else if (err.message) {
        errMessage = err.message;
        const finalErrMsg = `jwtFetcher.fetchJwtToken - jarvis error was ${errMessage}`;
        console.error(finalErrMsg);
        throw err;
    } else {
      throw err;
    }
  }

  return token;
}
