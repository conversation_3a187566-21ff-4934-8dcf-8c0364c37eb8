import { tBrand } from 'app/models/aemRules';
import { createFGAxiosInstance, getFulfillmentApiCredentials } from './customerInfoFetcher';
import { getParentChannelId } from 'app/helpers';
import { fgProductLite, GwsDomain } from 'app/models';
import { EVENT_MESSAGES, EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';
import { sfLogger } from 'app/logger';

export const fetchDomainsEligibleForGWS = async (
  currentEnv: string,
  domainsToCheck: fgProductLite[],
  brand: tBrand,
  userId: string,
  surface: string,
  rngTrace: string,
): Promise<fgProductLite[]> => {
  let errCode = null;
  let errMessage = null;
  let errMessageCode = null;
  let eligibleDomainsForGWS: fgProductLite[] = [];

  try {
    // Get credentials from config for FG calls later
    const credential = getFulfillmentApiCredentials(currentEnv);
    // Create axios instance with FG credentials
    const axiosJarvisFG = createFGAxiosInstance(currentEnv);

    // Creating payload here
    const payloadCredentials = {
      systemId: credential.systemId,
      systemPassword: credential.systemPassword,
      privateLabelId: 10,
      parentChannelId: getParentChannelId(brand),
    };

    const requests = domainsToCheck.map(async (domainToCheckObj) => {
      const domainName = domainToCheckObj.prodInstName;
      const data = {
        credential: payloadCredentials,
        domainName,
      };

      try {
        const response = await axiosJarvisFG.post(
          '/api/products/domains/checkDomainEligibleForGoogleWS',
          data,
        );
        return { domainToCheckObj, responseData: response.data };
      } catch (error) {
        return { domainToCheckObj, responseData: null, error };
      }
    });

    const results = await Promise.all(requests);

    let isSuccess = false;

    results.forEach(({ domainToCheckObj, responseData }) => {
      if (responseData && responseData?.eligibleForDirectPurchase) {
        eligibleDomainsForGWS.push(domainToCheckObj);
      }
    });

    sfLogger.info(
      {
        eventType: EVENT_TYPES.GwsEligibilityCheck,
        message: EVENT_MESSAGES.DomainsForGwsEligibility,
        customAttributes: {
          brand,
          userId,
          surface,
          totalDomainsSentToAM: eligibleDomainsForGWS.length,
          domainsReceivedFromGwsEligibility: eligibleDomainsForGWS.map(
            (domain) => domain.prodInstName,
          ),
        },
      },
      rngTrace,
    );

    isSuccess = results.some(({ responseData }) => responseData !== null);
  } catch (err: any) {
    if (err.response && err.response.status) {
      errCode = err.response.status;
      if (err.response.data.message) {
        errMessage = err.response.data.message;
      } else if (err.response.data.errorMessage) {
        errMessage = err.response.data.errorMessage;
      }

      if (errMessage && err.response.data.code) {
        errMessageCode = err.response.data.code;
        errMessage = `${errMessage}, code = ${errMessageCode}`;
      }
      const finalErrMsg = `domainsEligibilityFetcher.fetchDomainsEligibleForGWS - jarvis error was ${errMessage}`;
      console.error(finalErrMsg);
      throw new Error(finalErrMsg);
    } else if (err.message) {
      errCode = 500;
      errMessage = err.message;
      const finalErrMsg = `domainsEligibilityFetcher.fetchDomainsEligibleForGWS - jarvis error was ${errMessage}`;
      console.error(finalErrMsg);
      throw new Error(finalErrMsg);
    }
    throw err;
  }
  return eligibleDomainsForGWS;
};
