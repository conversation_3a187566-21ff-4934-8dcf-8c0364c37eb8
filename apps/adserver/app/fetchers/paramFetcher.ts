import { getConfigForEnv } from '../helpers/configHelper';
import { environmentConfig } from 'app/models';
import { decrypt } from '../helpers/cryptoUtil';

export function getParam(currentEnv: string, key: string) {
  const configFile: environmentConfig | undefined = getConfigForEnv(currentEnv);

  if (configFile === undefined) {
    throw new Error(
      `paramFetcher:getParam() - configFile is undefined for env=${currentEnv}`,
    );
  }

  let value: string = '';
  if (key === 'fulfillmentApiPassword') {
    value = configFile.fulfillmentApiPassword;

    if (!value) {
      throw new Error(
        `paramFetcher:getParam() - fulfillmentApiPassword is undefined for env=${currentEnv}`,
      );
    }
  } else if (key === 'fulfillmentApiKey') {
    // The additional API Key to talk to FG services is only needed when talking
    //  to FG from clients running on a cloud provider
    value = configFile.fulfillmentApiKey;
  } else if (key === 'jwtJarvisClientId') {
    value = configFile.jwtJarvisClientId;
    if (!value) {
      throw new Error(
        `paramFetcher:getParam() - jwtJarvisClientId is undefined for env=${currentEnv}`,
      );
    }
  } else {
    throw new Error(`paramFetcher:getParam() - key ${key} is not recognized`);
  }

  let decrypted;
  if (value) {
    decrypted = decrypt(value);
  } else {
    decrypted = '';
  }
  return decrypted;
}
