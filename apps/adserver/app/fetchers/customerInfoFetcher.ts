import axios from 'axios';
import { getConfigForEnv } from '../helpers/configHelper';
import { environmentConfig, iUsers } from 'app/models';
import { getParam } from './paramFetcher';
import { PROD } from '../constants';

export function createFGAxiosInstance(currentEnv: string) {
  const configFile: environmentConfig | undefined = getConfigForEnv(currentEnv);

  if (configFile === undefined) {
    throw new Error(
      `customerInfoFetcher:createFGAxiosInstance() - configFile is undefined for env=${currentEnv}`,
    );
  }

  const baseURL = configFile.fulfillmentApi;

  const timeout = configFile.fulfillmentApiTimeout;

  return axios.create({
    baseURL,
    timeout,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

function getFGApiAccessHeaderKey(currentEnv: string): string {
  if (currentEnv === PROD || currentEnv === 'production') {
    return getParam(currentEnv, 'fulfillmentApiKey');
  } else {
    return '';
  }
}

/**
 * fulfillment gateway auth. This credential gets added to the request body
 */
export function getFulfillmentApiCredentials(currentEnv: string) {
  // get the FG password for nfd-adserver.
  const systemPassword = getParam(currentEnv, 'fulfillmentApiPassword');

  const configFile: environmentConfig | undefined = getConfigForEnv(currentEnv);

  if (configFile === undefined) {
    throw new Error(
      `customerInfoFetcher:getFulfillmentApiCredentials() - configFile is undefined for env=${currentEnv}`,
    );
  }

  if (!systemPassword) {
    throw new Error(
      `customerInfoFetcher:getFulfillmentApiCredentials() - systemPassword is undefined for env=${currentEnv}`,
    );
  }

  return {
    systemId: parseInt(configFile.fulfillmentApiID),
    systemPassword,
    systemPasswordType: 'CLEARTEXT',
  };
}

/**
 * query jarvis to retrieve the customer account
 */
export async function queryProductsForSingleAccount(currentEnv: string, accountId: string) {
  let responseData = null;
  let isSuccess = false;
  let responseDataOutput = null;
  let errCode = null;
  let errMessage = null;
  let errMessageCode = null;

  try {
    const credential = getFulfillmentApiCredentials(currentEnv);

    const data = {
      credential,
      queryProductsForSingleAccountWrapperReq: {
        accountId,
        searchCriterion: {
          type: 'SearchCriterion',
          maxNumberOfItems: 2000,
          sortCriteria: [
            {
              isAscendingOrder: true,
              sortType: 20,
            },
          ],
          startIndex: 0,
          totalThreshold: 2000,
        },
      },
    };

    const fgApiAccessKey = getFGApiAccessHeaderKey(currentEnv) || '';

    let headers = {};
    if (fgApiAccessKey) {
      headers = {
        'X-API-Key': fgApiAccessKey,
      };
    }
    const axiosJarvisFG = createFGAxiosInstance(currentEnv);
    responseData = await axiosJarvisFG.post('/api/products/queryProductsForSingleAccount', data, {
      headers,
    });
    isSuccess = true;
    responseDataOutput = responseData.data;
  } catch (err: any) {
    if (err.response && err.response.status) {
      errCode = err.response.status;
      if (err.response.data.message) {
        errMessage = err.response.data.message;
      } else if (err.response.data.errorMessage) {
        errMessage = err.response.data.errorMessage;
      }

      if (errMessage && err.response.data.code) {
        errMessageCode = err.response.data.code;
        errMessage = `${errMessage}, code = ${errMessageCode}`;
      }
      const finalErrMsg = `customerInfoFetcher.queryProductsForSingleAccount - jarvis error was ${errMessage}`;
      console.error(finalErrMsg);
      throw new Error(finalErrMsg);
    } else if (err.message) {
      errCode = 500;
      errMessage = err.message;
      const finalErrMsg = `customerInfoFetcher.queryProductsForSingleAccount - jarvis error was ${errMessage}`;
      console.error(finalErrMsg);
      throw new Error(finalErrMsg);
    }
    throw err;
  }

  /* package the response in a consistent way */
  const customerData = {
    status: isSuccess ? 'success' : 'error',
    errorCode: errCode,
    data: responseDataOutput,
    errMessage,
  };
  return customerData;
}

/**
 * query jarvis to retrieve the customer accounts
 */
export async function queryAccountsForUser(currentEnv: string, userId: string) {
  let responseData = null;
  let isSuccess = false;
  let responseDataOutput = null;
  let errCode = null;
  let errMessage = null;
  let errMessageCode = null;

  try {
    const credential = getFulfillmentApiCredentials(currentEnv);

    const fgApiAccessKey = getFGApiAccessHeaderKey(currentEnv) || '';

    let headers = {};
    if (fgApiAccessKey) {
      headers = {
        'X-API-Key': fgApiAccessKey,
      };
    }

    const data = {
      credential,
      userAccountRequest: {
        userId,
        searchCriterion: {
          type: 'SearchCriterion',
          maxNumberOfItems: 100,
          sortCriteria: [
            {
              isAscendingOrder: true,
              sortType: 16,
            },
          ],
          startIndex: 0,
          totalThreshold: 100,
        },
      },
    };

    const axiosJarvisFG = createFGAxiosInstance(currentEnv);

    responseData = await axiosJarvisFG.post('/api/accounts/queryAccountsForUser', data, {
      headers,
    });

    isSuccess = true;
    responseDataOutput = responseData.data;
  } catch (err: any) {
    if (err.response && err.response.status) {
      errCode = err.response.status;
      if (err.response.data.message) {
        errMessage = err.response.data.message;
      } else if (err.response.data.errorMessage) {
        errMessage = err.response.data.errorMessage;
      }

      if (errMessage && err.response.data.code) {
        errMessageCode = err.response.data.code;
        errMessage = `${errMessage}, code = ${errMessageCode}`;
      }
      const finalErrMsg = `customerInfoFetcher.queryAccountsForUser - jarvis error was ${errMessage}`;
      console.error(finalErrMsg);
      throw new Error(finalErrMsg);
    } else if (err.message) {
      errCode = 500;
      errMessage = err.message;
      const finalErrMsg = `customerInfoFetcher.queryAccountsForUser - jarvis error was ${errMessage}`;
      console.error(finalErrMsg);
      throw new Error(finalErrMsg);
    }
    throw err;
  }

  /* package the response in a consistent way */
  const customerData = {
    status: isSuccess ? 'success' : 'error',
    errorCode: errCode,
    data: responseDataOutput,
    errMessage,
  };

  return customerData;
}

/**
 * query jarvis to retrieve the user data.
 */
export async function queryUser(currentEnv: string, userId: string): Promise<iUsers[]> {
  let responseData = null;
  let isSuccess = false;
  let responseDataOutput = null;
  let errMessage = null;
  let errMessageCode = null;

  try {
    const credential = getFulfillmentApiCredentials(currentEnv);

    const fgApiAccessKey = getFGApiAccessHeaderKey(currentEnv) || '';

    let headers = {};
    if (fgApiAccessKey) {
      headers = {
        'X-API-Key': fgApiAccessKey,
      };
    }

    const data = {
      credential,
      userQueryRequest: {
        includeAccountsRelations: true,
        includeUserDetail: true,
        userId,
      },
    };

    const axiosJarvisFG = createFGAxiosInstance(currentEnv);

    responseData = await axiosJarvisFG.post('/api/users/queryUser', data, {
      headers,
    });

    isSuccess = true;
    responseDataOutput = responseData?.data;
    return responseDataOutput;
  } catch (err: any) {
    errMessage = err?.response?.data?.message || err?.response?.data?.errorMessage || err?.message;

    if (errMessage) {
      errMessageCode = err?.response?.data?.code;
      errMessage = `${errMessage}, code = ${errMessageCode}`;
    }
    const finalErrMsg = `customerInfoFetcher.queryUser - jarvis error was ${errMessage}`;
    console.error(finalErrMsg);
    throw new Error(finalErrMsg);
  }
}
