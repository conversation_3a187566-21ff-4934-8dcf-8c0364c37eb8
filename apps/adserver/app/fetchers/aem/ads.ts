// const axios = require('axios')
// const { getHost } = require('../../helpers')
// const cheerio = require('cheerio')

import axios from 'axios';
import { getHost } from '../../helpers';
import { fetchBestMatchAdPath } from 'app/helpers/adFetchHelpers/fetchBestMatchAdPath';
import { fetchAndRefineBestMatchAd } from 'app/helpers/adFetchHelpers/adRefineUtils';

const aemEndpoint = {
  brand: 'https://www.bluehost.com/',
};

// Factory to produce our brand specific clients
function createAxiosInstance(baseURL: string) {
  return axios.create({
    baseURL,
    timeout: 5000,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

const brandClient = createAxiosInstance(aemEndpoint.brand);

brandClient.interceptors.request.use(
  (request) => {
    // Useful for debugging
    // console.log('Starting Request', JSON.stringify(request, null, 2))
    return request;
  },
  (error) => {
    console.log('Request error', error.message);
    return Promise.reject(error);
  },
);
export type tLogDecision = (log: string) => void;

const aemAdFetcher = async (args: any) => {
  const {
    host,
    referer,
    brand,
    path,
    env,
    tags = [],
    adPath,
    templateTokensOverrides,
    useDisabledAds,
    fetchLogic,
  } = args;

  // console.log(`Info: Making call to AEM: ${aemRequestPath}`);

  let response;

  let adTokenConfig: any;

  let bestMatch; // Path of our winning ad

  // Log all our decisions into this variable for debugging
  let decisionLogic = '';
  const logDecision: tLogDecision = (log: string) => {
    try {
      decisionLogic += `${log}<br />`;
    } catch (err) {}
  };

  let retVal: any = { ad: 'notfound' };

  // If we have been given an ad to fetch, we can bypass the smarts of calling the adServer to determine the ad to show.
  if (adPath) {
    bestMatch = adPath;
    logDecision(`Explicit path for ad passed in: ${adPath}`);
  } else {
    // We need to fetch the AEM config and see if this request has an appropriate ad to serve
    let hasError: boolean | unknown = false;
    const bestMatchResponse = await fetchBestMatchAdPath(
      brandClient,
      adTokenConfig,
      logDecision,
      bestMatch,
      useDisabledAds,
      referer,
      host,
      path,
      tags,
    );
    bestMatch = bestMatchResponse.bestMatch;
    response = bestMatchResponse.response;
    hasError = bestMatchResponse.hasError;
    if (hasError) return new Error(`Failed to make call to AEM to fetch Ad config ${hasError}`);
  }

  // If we have a match we want to return the AD
  if (bestMatch && !fetchLogic) {
    retVal = await fetchAndRefineBestMatchAd(
      bestMatch,
      brandClient,
      adTokenConfig,
      templateTokensOverrides,
      logDecision,
      brand,
      env,
    );
    if (retVal instanceof Error) {
      retVal = '';
    }
  } else if (fetchLogic) {
    retVal = decisionLogic;
  }
  return retVal;
};

export default aemAdFetcher;
