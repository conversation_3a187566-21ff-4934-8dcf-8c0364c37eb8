import express from 'express';
import compression from 'compression';
import { getCorsOptions, shouldCompress } from './helpers';
import cors from 'cors';

export const applyMiddlewares = (app: express.Application): void => {
  app.disable('x-powered-by');
  app.set('json spaces', 2);
  app.use(express.json());
  app.use(compression({ filter: shouldCompress }));
  app.use(cors(getCorsOptions()));
  app.use('/static', express.static('public'));
  app.use((req, res, next) => {
    res.setHeader('Newfold-Service', 'adServer');
    next();
  });
};
