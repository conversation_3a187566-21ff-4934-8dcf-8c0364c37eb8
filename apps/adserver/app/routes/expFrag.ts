import { CONFIGS, EXP_FRAG_CONFIGS } from 'app/cache/configCache';
import {
  BRANDS,
  DEVELOPMENT,
  ERROR_MESSAGES,
  EVENT_TYPES,
  PROD,
  QA,
  SFLOGGER_IDS,
  STAGE,
  JARVISQA1,
} from 'app/constants';
import { getRngString, sfLogger } from 'app/logger';
import { Env, tBrand } from 'app/models/aemRules';
import { Router, Request, Response } from 'express';
const expFragRouter: Router = Router();

const getExpFrag = async (req: Request, res: Response): Promise<any> => {
  const { brand, env, expFrag } = req.body;
  let adPath: string;
  let response;
  const rngTrace = getRngString();

  try {
    // Validate brand and env
    const validation = validateRequestBody(req.body);
    if (validation?.error) {
      sfLogger.warn(
        {
          eventType: EVENT_TYPES.Validation_Fail,
          message: ERROR_MESSAGES.ValidationFailed,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_47,
            validationError: validation.error,
            requestBody: req.body,
          },
        },
        rngTrace,
      );
      return res.status(400).json({ message: validation.error });
    }

    // If expFrag is an array, we need to fetch each fragment separately
    // else, we fetch it directly.
    if (Array.isArray(expFrag)) {
      const expFragRequests = expFrag.map(async (frag) => {
        adPath = `${brand}-blogs-${frag}.html`;
        return fetchExpFrag(env, brand, adPath, res);
      });

      response = await Promise.all(expFragRequests);
    } else {
      adPath = `${brand}-blogs-${expFrag}.html`;
      response = await fetchExpFrag(env, brand, adPath, res);
    }
    return res.json({
      status: 'success',
      response,
    });
  } catch (err: any) {
    sfLogger.error(
      {
        eventType: EVENT_TYPES.ExpFragError,
        message: ERROR_MESSAGES.GetExpFragError,
        errorDetails: (err as Error).message,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_46,
          brand,
          env,
        },
      },
      rngTrace,
    );
    console.log(`${ERROR_MESSAGES.GetExpFragError}: ${err.message}`);
    throw err;
  }
};

const validateRequestBody = (body: any) => {
  const { env, brand, expFrag } = body;
  if (
    !(
      (env as string) === PROD ||
      (env as string) === STAGE ||
      (env as string) === QA ||
      (env as string) === DEVELOPMENT ||
      (env as string) === JARVISQA1
    )
  ) {
    return { valid: false, error: 'Invalid env. Examples: qa, stg, prod, development' };
  }
  if (!BRANDS.includes(brand)) {
    return { valid: false, error: 'Invalid brand' };
  }
  if (!expFrag || (typeof expFrag !== 'string' && !Array.isArray(expFrag))) {
    return { valid: false, error: 'Invalid experience fragment path' };
  }
};

const fetchExpFrag = async (env: Env, brand: tBrand, adPath: string, res: Response) => {
  const fragment = (await EXP_FRAG_CONFIGS[env as Env][brand as tBrand]?.getAd(adPath)) || null;
  return fragment;
};
expFragRouter.post('/', getExpFrag);

export default expFragRouter;
