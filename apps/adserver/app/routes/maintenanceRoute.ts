import { flushCache } from 'app/cache/configCache';
import { IRouter, Router } from 'express';

const maintenanceRouter: IRouter = Router();

/**
 * We dont need anything fancier than this for now
 */
maintenanceRouter.get('/clearCache', (req, res) => {
  console.log('req.query.nfdPass', req.query);
  if (req.query.nfdPass === 'NEWFOLD_r@nd0mp@ss123') {
    flushCache();
    res.send('clearing cache...');
  } else {
    res.sendStatus(404);
  }
});

maintenanceRouter.get('/', (req, res) => {
  res.status(200).send('Maintenance mode');
});

export default maintenanceRouter;
