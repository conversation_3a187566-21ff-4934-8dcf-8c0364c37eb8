export interface iProduct {
  hideFromUI: boolean;
  renewalPrice: number;
  showTerm: boolean;
  domainHostingProductId: number;
  serviceTerms: string[];
  productInstanceId: string;
  productName: string;
  showRenewablePrice: boolean;
  isTldSupportedForHammerLogic: boolean;
  icannFee: IcannFee;
  coupons: any[];
  productOrderActionType: string;
  isDEPActive: boolean;
  isPRActive: boolean;
  productType: string;
  channelId: number;
  currentSelectedTerm: string;
  packagePath: string;
  showProductSpecificNotes: boolean;
  aftermarketDomain: boolean;
  productOriginalPrice: iProductOriginalPrice;
  productId: number;
  packageId: number;
  index: number;
  hidePrice: boolean;
  tld: string;
  productDesc: string;
  transactionType: string;
  hasSolution: boolean;
  premiumDomain: boolean;
  is28DayProduct: boolean;
  productCode: string;
  productDiscountPrice: iProductDiscountPrice;
  showDelete: boolean;
  domainName: string;
  refId: number;
  eligibleForPR: boolean;
}

export interface IcannFee {
  unitPrice: number;
  price: number;
}

export interface iProductOriginalPrice {
  price: number;
  postfix: string;
  monthlyPrice: number;
}

export interface iProductDiscountPrice {
  price: number;
  postfix: string;
  monthlyPrice: number;
}

export interface iCard {
  id: number;
  type: string;
  sku: string;
  products: iProduct[];
}
