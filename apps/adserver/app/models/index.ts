import { iPricingBffTerm } from 'app/helpers/pricingBffHelper';
import { Env } from './aemRules';

export interface iAdMatchers {
  wildcard?: Elements;
  tags?: Elements;
}

export interface Root {
  entities: Entity[];
  links: Link2[];
  class: string[];
  actions: Action[];
  properties: Properties2;
}

export interface Entity {
  links: Link[];
  class: string[];
  properties: Properties;
}

export interface Link {
  rel: string[];
  href: string;
}

export interface Properties {
  contentFragment: boolean;
  metadata: Metadata;
  createdBy: string;
  elementsOrder: string[];
  created: number;
  elements: Elements;
  name: string;
  description: string;
  modified: number;
  modifiedBy: string;
  title: string;
}

export interface Metadata {}

export interface Elements {
  adConfig: AdConfig;
  daysRemaining: DaysRemaining;
  notes: Notes;
  daysRemainingOperator: DaysRemainingOperator;
  adPlacement: AdPlacement;
  disabled: Disabled;
  priority: Priority;
  tokenizedData: TokenizedData;
  tags: Tags;
}

export interface AdConfig {
  variationsOrder: any[];
  ':type': string;
  variations: Variations;
  dataType: string;
  title: string;
  multiValue: boolean;
  value: string;
}

export interface Variations {}

export interface DaysRemaining {
  variationsOrder: any[];
  ':type': string;
  variations: Variations2;
  dataType: string;
  title: string;
  multiValue: boolean;
  value?: number;
}

export interface Variations2 {}

export interface Notes {
  variationsOrder: any[];
  ':type': string;
  variations: Variations3;
  dataType: string;
  title: string;
  multiValue: boolean;
  value?: string;
}

export interface Variations3 {}

export interface DaysRemainingOperator {
  variationsOrder: any[];
  ':type': string;
  variations: Variations4;
  dataType: string;
  title: string;
  multiValue: boolean;
  value?: string;
}

export interface Variations4 {}

export interface AdPlacement {
  variationsOrder: any[];
  ':type': string;
  variations: Variations5;
  dataType: string;
  title: string;
  multiValue: boolean;
  value: string;
}

export interface Variations5 {}

export interface Disabled {
  variationsOrder: any[];
  ':type': string;
  variations: Variations6;
  dataType: string;
  title: string;
  multiValue: boolean;
  value: boolean;
}

export interface Variations6 {}

export interface Priority {
  variationsOrder: any[];
  ':type': string;
  variations: Variations7;
  dataType: string;
  title: string;
  multiValue: boolean;
  value?: number;
}

export interface Variations7 {}

export interface TokenizedData {
  variationsOrder: any[];
  ':type': string;
  variations: Variations8;
  dataType: string;
  title: string;
  multiValue: boolean;
  value?: string;
}

export interface Variations8 {}

export interface Tags {
  variationsOrder: any[];
  ':type': string;
  variations: Variations9;
  dataType: string;
  title: string;
  multiValue: boolean;
  value?: string[];
}

export interface Variations9 {}

export interface Link2 {
  rel: string[];
  href: string;
}

export interface Action {
  method: string;
  name: string;
  href: string;
  title: string;
  fields?: Field[];
  type?: string;
}

export interface Field {
  name: string;
  type: string;
}

export interface Properties2 {
  name: string;
  'cq:conf': string;
  sourcing: string;
  'srn:paging': SrnPaging;
}

export interface SrnPaging {
  total: number;
  offset: number;
  limit: number;
}

export interface tTemplateTokenOverrides {
  token: {
    tokenName: string;
    tokenValue: string;
  };
}

export interface iAdServerGetAd {
  useDisabledAds: string | boolean;
  host?: string;
  path?: string;
  referer?: string | undefined;
  tags: string;
  adPath: string;
  fetchLogic: string | boolean;
  env: string;
  templateTokensOverrides: tTemplateTokenOverrides[];
  brand: string;
}

export interface Product {
  hideFromUI: boolean;
  showAddCpanel?: boolean;
  showTerm: boolean;
  serviceTerms: string[];
  productInstanceId: string;
  showAddPlesk?: boolean;
  productName: string;
  showRenewablePrice: boolean;
  features?: string[];
  domainName: string;
  productOrderActionType: string;
  productType: string;
  channelId: number;
  currentSelectedTerm: string;
  packagePath: string;
  showProductSpecificNotes: boolean;
  productOriginalPrice: {
    price: number;
    postfix: string;
    monthlyPrice: number;
  };
  parentProductId?: number;
  productId: number;
  packageId: number;
  priceType?: string;
  index: number;
  hidePrice: boolean;
  productDesc: string;
  transactionType: string;
  hasSolution: boolean;
  is28DayProduct: boolean;
  productCode: string;
  productDiscountPrice: {
    price: number;
    postfix: string;
    monthlyPrice: number;
  };
  showDelete: boolean;
  refId: number;
  attrUniqueId?: number;
  coupons?: string[];
  accountId: number;
}

export interface Card {
  id: number;
  type: string;
  products: Product[];
}

export interface Cart {
  cards: Card[];
  currencyCode: string;
}

export interface NewCartAd {
  name: string;
  pricingText1: string;
  pricingText2: string;
  sku: string;
  siteId: string | undefined;
  landingCode: string;
  modalDetails: ModalDetails;
  coupon?: string;
  channelId: number;
}

export interface ModalDetails {
  title: string;
  message: string;
  matToolTipText: string;
}
export interface NewCartAdsData {
  name: string;
  productCode: string;
  pricingTerm: iPricingBffTerm;
  priceDisplayTerm1: iPricingBffTerm;
  priceDisplayTerm2: iPricingBffTerm;
  pricingText1: string;
  pricingText2: string;
  pricingSiteId: number;
  coupon?: string;
  landingCode: string;
  modalDetails: ModalDetails;
}

export interface NewCartAdResponse {
  status: string;
  response: NewCartResObj[];
}
export interface NewCartResObj {
  adDetails: NewCartAd;
}
export interface PriceTerms {
  termQuantity: number;
  termUnit: number;
  termUnitDescription: iPricingBffTerm;
  finalPrice: string;
  basePrice: string;
  regularRate: string;
  monthlyPrice: string;
  monthlyBasePrice: string;
  totalDiscount: string;
}

export interface RawProductPrice {
  terms: PriceTerms[];
  currencySymbol: string;
}

export interface XSellRequestBody {
  containerName: string;
  brand: string;
  responseType: 'fragId' | 'frag' | 'rawFrag';
  cart: Cart;
  channel?: string;
  env: Env;
  isLoggedIn: boolean;
  accountId: string;
  userId: string;
  isLargeUser: boolean;
  countryCode: string;
  isAffiliate: boolean;
  reDirectToPage: string;
  testOffers: string[];
  currencyCode: string;
  isFirstLogin: boolean | null;
  isCSR: boolean | undefined;
  newCart: boolean | undefined;
}

export type AdObj = {
  productSku?: string | string[];
  sku?: string;
  adType?: string;
  productMarkup?: iCacheItem;
  priority?: number;
  weightage?: number;
  actionType?: string;
  pricingTerm?: string;
  landingCode?: string;
  channelID?: string;
  coupon?: string;
  adIdentifier?: string;
  alias?: string;
  eligibleDomainsList?: GwsDomain[];
  packageEligible?: boolean;
  packageName?: string;
  disclaimer?: string;
  redirect?: string;
  siteId?: string | undefined;
  alternateDomains?: string[];
};

export type AdResponse = {
  adDetails: AdObj;
};

export interface iCacheItem {
  htmlString?: string;
  headContent?: string;
  bodyContent: string;
  linkTags: Array<Record<string, string>>;
  scriptTags: Array<Record<string, string>>;
}

export interface environmentConfig {
  pricingApiTimeout: number;
  pricingApi: string;
  fulfillmentApi: string;
  fulfillmentApiID: string;
  fulfillmentApiPassword: string;
  fulfillmentApiTimeout: number;
  fulfillmentApiKey: string;
  aslApi: string;
  aslApiTimeout: number;
  hostingApi: string;
  hostingApiTimeout: number;
  jwtJarvisClientName: string;
  jwtJarvisClientId: string;
  jwtJarvisTokenExpirationTime: number;
  jwtJarvisEnvironment: string;
  jwtJarvisTokenUrl: string;
  jwtApiTimeout: number;
}

export interface fgProductLite {
  prodCode: string;
  prodInstName: string;
  lifecycleCdId: number;
  financialCdId: number;
  createdDate: string;
  autoRenewFlag: boolean;
  prodType: string;
  prodInstId: string;
  accountId: number;
}

export interface GwsDomain {
  accountId?: number;
  prodInstId?: string;
  prodInstName: string;
  prodType?: string;
}

export interface iUsers {
  user: {
    firstName: string;
    lastName: string;
    email: string;
    userId: string;
    addresses: any;
    phone: any;
  };
}

export interface productEligibilityResult {
  isEligibilityCheckPassed: boolean;
  statusMessage: string;
}

// used when we want to translate the env to other env for config
export interface configEnv {
  env: string;
  configEnv: string;
}

export interface NewAdResponse {
  status: string;
  response: AdObj[];
}
