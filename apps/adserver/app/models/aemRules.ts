import { FREE, PROD, QA } from 'app/constants';
import { iPricingBffTerm } from 'app/helpers/pricingBffHelper';

export type tBrand = 'BLUEHOST' | 'HOSTGATOR' | 'NETWORKSOLUTIONS';
export type tContainerName = 'InCart' | 'AMHPCards' | 'AMWI';

export type Env = typeof PROD | typeof QA;

export interface iFragmentRule {
  packageName: string;
  adSku: string | string[];
  adType: string;
  sku: string;
  defaultSelectedPlan?: string;
  htmlPath: string;
  htmlPathQA: string;
}

export interface iRule {
  brand: tBrand;
  name: string;
  surface: string;
  containerName: string;
  actionType: string;
  url: string;
  productsInCart: string[];
  priority: number;
  packageEligible: boolean;
  fragmentJsonPath: string[];
  fragmentDetails: iFragmentRule;
  minDomainsInCart: number;
  cartIncludesAll: string[];
  cartIncludesAny: string[];
  cartShouldNotInclude: string[];
  pricingTerm: string;
  priceDisplayTerm: string;
  disclaimerCopy: string;
  landingCode: string;
  channelID: string;
  coupon: string;
  domainsInCartExceedIncludedSku: string[];
  redirect: string;
  businessRule: string;
  alias: string;
  checkDomainsEligibleGWS: boolean;
  maximumDomainsToCheckGWS: number;
  weightage: number;
  pricingSiteId: number;
  loggedInStatusRequired: string;
  notShowInFirstLogin: boolean;
}

export interface iAemRules {
  rules: iRule[];
}

export interface ConfigCacheResults {
  configs: iAemRules | undefined,
  statusMessages: string[],
}

export interface FilteredDBRules {
  adsNotServedToday: iRule[];
  adsServedToday: string[];
}

export interface iPricingRule {
  termQuantity: number;
  termUnit: number;
  termUnitDescription: iPricingBffTerm;
  finalPrice: string;
  basePrice: string;
  regularRate: string;
  monthlyPrice: string;
  monthlyBasePrice: string;
  totalDiscount: string;
}
type Price = typeof FREE | string | undefined;
export type ProductPrice = { price: Price; basePrice: string | undefined } | undefined;

export type tAltDomainParams = {
  brand: tBrand;
  currencyCode: string;
  domains: string;
  env: Env;
  useConfigTlds: boolean;
  spinSearch: boolean;
  aftermarketDomainsReq: boolean;
  registryPremium: boolean;
  includePremiumDomainsInTopTlds: boolean;
  spinDomainsWithoutTldsReq: boolean;
  aftermarket: boolean;
  client: string;
  tldOrder: string[];
};
