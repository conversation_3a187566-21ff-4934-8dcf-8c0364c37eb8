import adRoutes from './routes/adServer';
import crossSellRoutes from './routes/crossSell';
import express, { Express } from 'express';
import path from 'path';
import statusRoutes from './routes/status';
import xSellRouter from './routes/xSellRouter';
import { applyMiddlewares } from './middlewares';
import { errorHandler } from './handlers/error';
import { notFoundHandler } from './handlers/notFound';
import { version } from '../package.json';

import swaggerUi from 'swagger-ui-express';
import getSwaggerDoc from '../swagger';
import { getSwaggerHtml } from './constants';
import maintenanceRouter from './routes/maintenanceRoute';
import { isCacheFilled } from './cache/configCache';
import expFragRouter from './routes/expFrag';

const startSwagger = Boolean(process.env.SWAGGER) && String(process.env.SWAGGER)?.trim() === 'true';

const app: Express = express();
console.log(`Version=${version}`);

applyMiddlewares(app);

app.get('/adServer/status', statusRoutes);
app.get('/adServer/getAd', adRoutes);
app.get('/crossSell/get', crossSellRoutes);
app.use('/api/v1/getExpFrag', expFragRouter);
app.use('/api/v1/getXSell', xSellRouter);
app.use('/maintenance', maintenanceRouter);

/**
 * K8s readiness probe
 * we want to start sending requests only when the cache has been filled
 */
app.get('/ready', function (_, res): any {
  const ready = isCacheFilled();

  if (ready) {
    return res.json({
      ready,
    });
  } else {
    return res.status(404);
  }
});

app.get('/', (req, res) => {
  res.sendFile(path.resolve('.', 'public', 'index.html'));
});

if (startSwagger) {
  app.get('/apidocs', (req, res) => {
    res.redirect('/apidocs/cart');
  });

  app.get('/apidocs/swaggerJson/:surface', (req, res) => {
    const surface = req.params.surface;
    res.json(getSwaggerDoc(surface));
  });

  app.get('/apidocs/:surface', (req, res) => {
    const surface = req.params.surface;
    res.send(getSwaggerHtml(surface));
  });
}

app.use(notFoundHandler);
app.use(errorHandler);

export default app;
