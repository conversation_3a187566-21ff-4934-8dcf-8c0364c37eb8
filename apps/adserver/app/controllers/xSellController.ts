import { Request, Response } from 'express';
import { getAllowListedDomains } from '../helpers';
import { xSellServiceUppCart } from 'app/services/xSellServiceUppCart';
import { xSellServiceAMHPCards } from 'app/services/xSellServiceAMHPCards';
import { xSellServiceAMWI } from 'app/services/xSellServiceAMWI';
import { validateRequestBody } from 'app/helpers/validateRequestBody';
import { XSellRequestBody } from 'app/models';
import { getRngString, sfLogger } from 'app/logger';
import { ERROR_MESSAGES, EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';
import { xSellServiceTestOffers } from 'app/services/xSellServiceTestOffers';
import { xSellServiceNewCart } from 'app/services/xSellServiceNewCart';

export async function xSellController(req: Request, res: Response): Promise<any> {
  const xSellRequestBody = req.body as XSellRequestBody;
  const { containerName, brand, userId, accountId, testOffers, newCart } = xSellRequestBody;
  const hostname = req.headers.host || '';
  const rngTrace = getRngString();

  let retVal: any = { ad: 'notfound' };

  try {
    let responseType = (xSellRequestBody.responseType as string) ?? '';

    // perform common validation
    const validation = validateRequestBody(xSellRequestBody);
    if (!validation.valid) {
      sfLogger.warn(
        {
          eventType: EVENT_TYPES.Validation_Fail,
          message: ERROR_MESSAGES.ValidationFailed,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_05,
            validationError: validation.error,
            requestBody: xSellRequestBody,
            userId: userId ?? 'not passed',
            accountId: accountId ?? 'not passed',
          },
        },
        rngTrace,
      );
      return res.status(400).json({ message: validation.error });
    }

    // We can start modifying the cart controller code to check
    // for cartIncludes, cartDoesNotInclude and minDomainsInCart
    // retrieved from AEM and make a decision which Ad's HTML to send.
    if (testOffers?.length) {
      retVal = await xSellServiceTestOffers(xSellRequestBody, containerName, rngTrace);
    } else if (newCart) {
      retVal = await xSellServiceNewCart(xSellRequestBody, rngTrace);
    } else {
      switch (containerName) {
        case 'AMHPCards':
          retVal = await xSellServiceAMHPCards(xSellRequestBody, rngTrace);
          break;
        case 'InCart':
          retVal = await xSellServiceUppCart(xSellRequestBody, rngTrace);
          break;
        case 'AMWI':
          retVal = await xSellServiceAMWI(xSellRequestBody, rngTrace);
          break;
        default:
          sfLogger.error(
            {
              eventType: EVENT_TYPES.ContainerName_CE_1,
              message: ERROR_MESSAGES.ContainerNotFound,
              customAttributes: {
                initiator: SFLOGGER_IDS.SFLOG_ID_06,
                containerName,
                xSellRequestBody,
                userId: userId ?? 'not passed',
                accountId: accountId ?? 'not passed',
              },
            },
            rngTrace,
          );
          return res.status(404).json({
            msg: 'containerName not found',
          });
      }
    }

    // Response Handling
    if (responseType === 'rawFrag') {
      res.set('X-Frame-Options', 'SAMEORIGIN');
      res.set('Content-Security-Policy', `frame-ancestors 'self' ${getAllowListedDomains()};`);
      res.set('Content-Type', 'text/html');

      res.send(retVal);
    } else {
      retVal.trace = rngTrace;
      res.json(retVal);
    }
  } catch (err) {
    const message = (err as Error).message;
    sfLogger.error(
      {
        eventType: EVENT_TYPES.xSellController,
        message: ERROR_MESSAGES.ControllerError,
        errorDetails: message,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_08,
          cartCards: xSellRequestBody?.cart?.cards
            ?.map((c) => c.products)
            .flat()
            .map((prod) => {
              return {
                productCode: prod.productCode,
                productName: prod.productName,
              };
            }),
          containerName: containerName,
          brand: brand,
          userId: userId ?? 'not passed',
          accountId: accountId ?? 'not passed',
        },
      },
      rngTrace,
    );

    console.error(`Error in xSellController `, (err as Error).message, err);

    // allow input validation errors
    if (message && message.indexOf('400') !== -1) {
      return res.status(404).json({
        error: true,
        message,
      });
    }
    return res.status(404).json({
      error: true,
      message,
      err,
    });
  }
}
