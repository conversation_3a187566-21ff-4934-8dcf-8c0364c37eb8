import request from 'supertest';
import app from '../../index';
import { xSellServiceUppCart } from 'app/services/xSellServiceUppCart';
import xSellRequestBody from '../../../mock-data/getXSell-request-body.json';
import xSellUppServiceResponse from '../../../mock-data/getXSell-response-upp-service-.json';

jest.mock('app/services/xSellServiceUppCart', () => ({
  xSellServiceUppCart: jest.fn(),
}));

describe('xSellController', () => {
  it('1. should return success response from xSellServiceUppCart', async () => {
    const mockedXSellServiceUppCart = xSellServiceUppCart as jest.MockedFunction<
      typeof xSellServiceUppCart
    >;

    mockedXSellServiceUppCart.mockResolvedValue(xSellUppServiceResponse);

    const retVal = await request(app).post('/api/v1/getXSell').send(xSellRequestBody);

    expect(retVal.status).toBe(200);
    const productSku0 = retVal.body.response[0].adDetails.productSku;
    const productSku1 = retVal.body.response[1].adDetails.productSku;
    expect(productSku0).toBe('CODEGUARD_BASIC_V2');
    expect(productSku1).toBe('SITELOCK_ESSENTIALS');
  });

  it('2. should handle validation failure', async () => {
    let response;

    // invalid_brand
    const InvalidBrand = { ...xSellRequestBody, brand: 'invalid_brand' };
    response = await request(app).post('/api/v1/getXSell').send(InvalidBrand);
    expect(response.status).toBe(400);
    expect(response.body.message).toBe('Invalid brand');

    // invalid_ResponseType
    const InvalidResponseType = { ...xSellRequestBody, responseType: 'invalid_ResponseType' };
    response = await request(app).post('/api/v1/getXSell').send(InvalidResponseType);
    expect(response.status).toBe(400);
    expect(response.body.message).toBe('Invalid responseType. Examples: fragId or frag or rawFrag');
  });

  it('3. should return 400 for unsupported container name', async () => {
    let response;
    const invalidContainerName = { ...xSellRequestBody, containerName: 'Invalid_Container' };
    xSellRequestBody.responseType = 'invalid_ResponseType';
    response = await request(app).post('/api/v1/getXSell').send(invalidContainerName);
    expect(response.status).toBe(400);
    expect(response.body.message).toBe(
      'Invalid containerName. Examples: InCart or AMHPCards or AMWI',
    );
  });
});
