import express, { Request, Response } from 'express';
import xss from 'xss';

import AdServerService from '../services/adServerService';
import { getEnv, getAllowListedDomains } from '../helpers';
import { tTemplateTokenOverrides } from '../models';
import { ERROR_MESSAGES } from 'app/constants';

const adServerService = new AdServerService();

export async function getAdController(req: Request, res: Response): Promise<any> {
  const queryParams = req.query;

  /**
   * The KB had a weird issue where ads from one brand were showing
   * on another brand. We have a potential fix on the KB side, but during
   * debugging, we found that the URL
   *
   * http://localhost:8080/adServer/getAd/?
   *
   * triggered a network fetch when it shouldn't. Some instances even returned
   * a "Not Found" text. Even if this is not the cause, we want to fix it
   */
  if (!Object.keys(queryParams).length) {
    return res.status(404).json({
      status: 404,
      error: 'Not Found',
      message: ERROR_MESSAGES.ResourceNotFound,
      path: req.path,
    });
  }

  const hostOverride = xss(queryParams?.hostOverride as string);
  const tags = xss(queryParams?.tags as string);
  const useDisabledAds = xss(queryParams?.useDisabledAds as string) || false;
  const adPath = xss(queryParams?.adPath as string);
  const fetchLogic = xss(queryParams?.fetchLogic as string) || false;
  const host = req.protocol + '://' + (hostOverride || req.get('host'));
  const path = req.path;
  const referer = queryParams?.refererOverride || req.get('Referrer');
  const brand: string = xss(queryParams?.brand as string) || 'bluehost';

  // const env = getEnv(req.headers.host || ''); // will return 'development' if not present
  const env = String(queryParams.env) || 'prod';
  const templateTokensOverrides: tTemplateTokenOverrides[] = [];
  console.log(
    `Host on request is ${host}${path} and referer=${referer} refererOverride was ${referer}`,
  );

  // loop through each query parameter
  for (const key in queryParams) {
    // if the key starts with 'token-'
    if (key.startsWith('token-')) {
      // get the token name and value
      const tokenName = xss(key.substring(6)); // remove 'token-' from the start

      const queryParamRaw = queryParams[key] as string;

      let decodedValue = xss(queryParamRaw);

      try {
        // second pass decoding
        decodedValue = decodeURIComponent(queryParamRaw);
      } catch (err) {
        // We are protecting ourselves from cases where a % might natually occur in a value
        // We still need to decodeURIComponent() above to handle cases where the value is something like
        // a valid URL containing query params of its own
      }

      templateTokensOverrides.push({
        token: {
          tokenName: tokenName,
          tokenValue: decodedValue,
        },
      });
    }
  }

  try {
    const ad = await adServerService.getAd({
      ...queryParams,
      host,
      path,
      referer: referer ? String(referer) : referer,
      tags,
      adPath,
      fetchLogic,
      env,
      templateTokensOverrides,
      useDisabledAds,
      brand: brand,
    });
    // if (ad.name === 'Error') {
    // res.sendStatus(404);
    // } else if (ad.ad === 'notfound') {
    //   res.status(404).json({
    //     status: 404,
    //     error: 'Not Found',
    //     message: ERROR_MESSAGES.ResourceNotFound,
    //     path: req.path,
    //   });
    // } else {
    // Attach headers to allow iframe embedding
    res.set('X-Frame-Options', 'SAMEORIGIN');
    res.set('Content-Security-Policy', `frame-ancestors 'self' ${getAllowListedDomains()};`);
    res.set('Content-Type', 'text/html');
    res.send(ad);
    // }
  } catch (err) {
    console.log(`Error returned to controller ${JSON.stringify(err, null, '\t')}`);
    res.sendStatus(404);
  }
}
