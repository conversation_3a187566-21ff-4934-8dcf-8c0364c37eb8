import { iUsers } from 'app/models';
import {
  queryProductsForSingleAccount,
  queryAccountsForUser,
  queryUser,
} from '../fetchers/customerInfoFetcher';

export async function getProductsForSingleAccount(currentEnv: string, accountId: string) {
  return await queryProductsForSingleAccount(currentEnv, accountId);
}

export async function getAccountsForUser(currentEnv: string, userId: string) {
  return await queryAccountsForUser(currentEnv, userId);
}

export async function getUser(currentEnv: string, userId: string): Promise<iUsers[]> {
  return await queryUser(currentEnv, userId);
}
