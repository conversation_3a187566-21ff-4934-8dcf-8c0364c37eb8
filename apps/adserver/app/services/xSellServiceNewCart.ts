import { NEW_CART_CACHE } from 'app/cache/configCache';
import { ERROR_MESSAGES, EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';
import { getConfigEnv, isValidEnv } from 'app/helpers';
import { getAdCodesFromProduct } from 'app/helpers/cartHelpers';
import { isHosting } from 'app/helpers/productIdentifiers';
import { sfLogger } from 'app/logger';
import { configEnv, NewCartAd, NewCartAdResponse, XSellRequestBody } from 'app/models';
import { Env } from 'app/models/aemRules';

export const xSellServiceNewCart = async (xSellRequestBody: XSellRequestBody, rngTrace: string) => {
  const { brand, responseType, accountId } = xSellRequestBody;

  let response: NewCartAdResponse = { status: 'success', response: [] };
  let { env } = xSellRequestBody;
  const currentContainerName = 'InCart';
  const currencyCode =
    xSellRequestBody?.cart?.currencyCode || xSellRequestBody?.currencyCode || 'USD';

  // translate to correct env settings
  let configInfo: configEnv = getConfigEnv(env);
  const configEnv: string = configInfo.configEnv;
  env = configInfo.env.toLowerCase() as Env;

  const newCartAdsCache = NEW_CART_CACHE[env];

  try {
    if (!isValidEnv(env)) {
      console.error(ERROR_MESSAGES.envInvalid);
      sfLogger.error(
        {
          eventType: EVENT_TYPES.xSellServiceNewCart_CE_1,
          message: ERROR_MESSAGES.envInvalid,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_53,
            env,
          },
        },
        rngTrace,
      );
      throw Error(ERROR_MESSAGES.envInvalid);
    }

    // first we assemble all skus present in cart in an array
    const products = xSellRequestBody.cart.cards.map((c) => c.products).flat();
    const productCodes = products.map((p) => p.productCode);
    const productTypes = products.map((p) => p.productType);
    const productsSet = new Set(productCodes);
    const allCartSkus = Array.from(productsSet);

    const responseAds: Set<NewCartAd> = new Set();

    for (let i = 0; i < productCodes.length; i++) {
      const product = productCodes[i];
      const isHostingProduct = isHosting(product);

      if (isHostingProduct) {
        const adCodes = getAdCodesFromProduct(product);
        if (adCodes) {
          adCodes.forEach((code) => {
            const ad = newCartAdsCache.getAd(currencyCode, code);
            responseAds.add(ad);
          });
        }
      }
    }
    const responseArr = Array.from(responseAds).map((ad) => ({
      adDetails: ad,
    }));
    response.response = responseArr;
    return response;
  } catch (err: any) {
    const products = xSellRequestBody.cart.cards.map((c) => c.products).flat();
    sfLogger.error(
      {
        eventType: EVENT_TYPES.xSellServiceNewCart_CE_2,
        message: ERROR_MESSAGES.XSellServiceError,
        errorDetails: (err as Error).message,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_54,
          brand,
          responseType,
          env,
          cartCards: products,
          accountId: accountId ?? 'not passed',
        },
      },
      rngTrace,
    );
    console.error(`${ERROR_MESSAGES.XSellServiceError}: ${err.message}`);
    throw err;
  }
};
