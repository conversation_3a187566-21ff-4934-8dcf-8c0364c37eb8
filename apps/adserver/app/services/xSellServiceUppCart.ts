import { Env, iAemRules, iRule, tBrand, ConfigCacheResults } from 'app/models/aemRules';
import { CONFIGS, getConfigs } from '../cache/configCache';
import {
  arrayBisSubsetOfA,
  getGroupByRule,
  logSuccessResponse,
  getConfigEnv,
  getRandomizedRules,
} from 'app/helpers';
import {
  AdObj,
  AdResponse,
  Card,
  GwsDomain,
  Product,
  XSellRequestBody,
  configEnv,
} from 'app/models';
import { findDomainCodesInCart, findProductsInCart } from '../helpers/cartHelpers';
import { rejectUnallowedProducts } from 'app/helpers/rejectUnallowedProducts';
import { sfLogger } from 'app/logger';
import { ERROR_MESSAGES, EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';
import { isValidEnv } from '../helpers/index';
import BusinessRuleHelper from 'app/helpers/businessRuleHelper';
import { getProductsByAccountId } from 'app/helpers/getUserActiveProducts';
import { checkEligibleForGWS } from 'app/helpers/checkGWSInfo';
import { fgProductLite } from '../models/index';
import getAdsFromRules from 'app/helpers/adFetchHelpers/getAdsFromRules';
import { isLoggedInStatusAcceptable } from 'app/helpers/checkLoggedInStatusHelper';
import { AM_BUSINESS_RULES as amBusinessRules } from '../constants';
import { BUSINESS_RULE_PRODUCT_KEYS as productKeys } from '../constants';
import { LOGGED_IN_STATUS_ALLOWED_LOGGED_IN } from 'app/constants';

export async function xSellServiceUppCart(xSellRequestBody: XSellRequestBody, rngTrace: string) {
  let retVal: any = { ad: 'notfound' };

  const { brand, responseType, accountId, isLoggedIn, isLargeUser, isCSR } = xSellRequestBody;

  let { env } = xSellRequestBody;
  const currentContainerName = 'InCart';

  // translate to correct env settings
  let configInfo: configEnv = getConfigEnv(env);
  const configEnv: string = configInfo.configEnv;
  env = configInfo.env.toLowerCase() as Env;

  try {
    if (!isValidEnv(env)) {
      console.error(ERROR_MESSAGES.envInvalid);
      sfLogger.error(
        {
          eventType: EVENT_TYPES.xSellServiceUppCart_CE_3,
          message: ERROR_MESSAGES.envInvalid,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_34,
            env,
          },
        },
        rngTrace,
      );
      throw Error(ERROR_MESSAGES.envInvalid);
    }

    // first we assemble all skus present in cart in an array
    const products = xSellRequestBody.cart.cards.map((c) => c.products).flat();
    const productCodes = products.map((p) => p.productCode);
    const productTypes = products.map((p) => p.productType);
    const productsSet = new Set(productCodes);
    const allCartSkus = Array.from(productsSet);

    // Now fetch all AEM rules.
    const configCacheResults: ConfigCacheResults = await getConfigs(env, brand as tBrand);
    let AEM_RULES: iAemRules | undefined = configCacheResults.configs;

    if (!AEM_RULES) {
      const errors = configCacheResults.statusMessages.toString();
      sfLogger.error(
        {
          eventType: EVENT_TYPES.xSellServiceUppCart_CE_2,
          message: ERROR_MESSAGES.AemRulesLoadingFailed,
          errorDetails: errors,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_36,
            AEM_RULES,
            productCodes,
            products,
            cartCards: products,
            containerName: 'UPPCart',
          },
        },
        rngTrace,
      );
      throw new Error(ERROR_MESSAGES.AemRulesLoadingFailed + `: brand=${brand} : ` + errors);
    }
    const rulesCopy = structuredClone(AEM_RULES.rules);
    const allRules = getRandomizedRules(rulesCopy, currentContainerName, env);

    // Now we reject those AEM rules if the cart contains the rule's unallowed product codes (cartShouldNotInclude)
    const filteredAEMRules: iAemRules = rejectUnallowedProducts(allRules, allCartSkus);
    const selectedRules: string[] = [];

    // Now we simplify our cart a bit and save it in cartSkus variable
    const cartSkus = xSellRequestBody.cart.cards.map((card: Card) => ({
      id: card.id,
      productCodes: card.products.flatMap((product: Product) => product.productCode),
    }));

    // stage 1: match brand

    const matchingRules: iRule[] = [];

    // stage 2: match products
    const domainCodesInCart = findDomainCodesInCart(productTypes);

    let activeHosting: number = 0;
    let activeEcommHosting: number = 0;
    let purchHostingLast7Days: number = 0;
    let purchEcommLast7Days: number = 0;

    // load products for the account
    const businessRulesHelper = new BusinessRuleHelper();
    if (isLoggedIn && accountId && !isLargeUser) {
      const activeProducts = await getProductsByAccountId(
        accountId,
        configEnv,
        brand,
        responseType,
        rngTrace,
      );
      businessRulesHelper.loadProductMap(activeProducts);

      activeHosting = businessRulesHelper.getProductCount(productKeys.activeHosting);
      activeEcommHosting = businessRulesHelper.getProductCount(productKeys.activeEcommHosting);

      purchHostingLast7Days = businessRulesHelper.getProductCount(
        productKeys.purchHostingLast7Days,
      );
      purchEcommLast7Days = businessRulesHelper.getProductCount(productKeys.purchEcommLast7Days);
    }

    // Fetch all domains from the account which doesn't contain gws already and user is logged in
    let allDomainsForAccount: fgProductLite[] = businessRulesHelper.getDomainsForUser();
    let allGwsDomainsForAccount: string[] = businessRulesHelper.getGwsDomainsForUser();

    // cartSkus
    for (let idx = 0; idx < cartSkus.length; idx++) {
      const card = cartSkus[idx];
      // We don't want additional processing if we already have 3 matching rules
      if (matchingRules.length > 3 && !isCSR) break;
      for (let ruleCount = 0; ruleCount < filteredAEMRules?.rules.length; ruleCount++) {
        const rule = filteredAEMRules?.rules[ruleCount];

        // Skipping the rule if it is already selected
        if (selectedRules.includes(rule.name)) continue;
        if (matchingRules.length > 3 && !isCSR) break;

        let isRuleSelected;
        if (rule?.containerName !== currentContainerName) {
          isRuleSelected = false;
        } else {
          const ifAllProductsMatchTheRule = rule?.cartIncludesAll?.length
            ? arrayBisSubsetOfA(card.productCodes, rule.cartIncludesAll, 'all')
            : true;

          const isAnyProductInCartIncludes = rule?.cartIncludesAny?.length
            ? arrayBisSubsetOfA(card.productCodes, rule.cartIncludesAny, 'any')
            : true;

          const isProductInCartShouldNotInclude = rule?.cartShouldNotInclude?.length
            ? arrayBisSubsetOfA(card.productCodes, rule.cartShouldNotInclude, 'any')
            : false;

          // check if adSku is already a part of card or not, it should not be a part of card to be eligible

          let isAdSkuAlreadyPresent;
          if (Array.isArray(rule?.fragmentDetails?.adSku)) {
            isAdSkuAlreadyPresent = card.productCodes.some(
              (sku: string) => sku && rule.fragmentDetails.adSku.includes(sku),
            );
          } else {
            isAdSkuAlreadyPresent = card.productCodes.includes(rule?.fragmentDetails?.adSku);
          }

          // if minDomainsInCart is specified do some check of cart
          let isMinDomainCheckOk = true;
          let numDomainsInCart = 0;
          if (rule.minDomainsInCart && rule.minDomainsInCart > 0) {
            if (domainCodesInCart && domainCodesInCart.length) {
              numDomainsInCart = domainCodesInCart.length;
            }

            if (numDomainsInCart < rule.minDomainsInCart) {
              isMinDomainCheckOk = false;
            }
          }

          let domainsInCartExceedCheckNeeded = false;
          let domainsInCartExceedCheckOk = true;
          if (
            rule.domainsInCartExceedIncludedSku &&
            rule.domainsInCartExceedIncludedSku.length > 0
          ) {
            domainsInCartExceedCheckNeeded = true;
            const productsInCart = findProductsInCart(
              productCodes,
              rule.domainsInCartExceedIncludedSku,
            );
            if (domainCodesInCart.length <= productsInCart.length) {
              domainsInCartExceedCheckOk = false;
            }
          }

          const loggedInStatusRequired = rule?.loggedInStatusRequired;
          const loggedInStatusAcceptablePassed = isLoggedInStatusAcceptable(
            isLoggedIn,
            loggedInStatusRequired,
          );

          let businessRulePassed = true;

          if (
            isLoggedIn &&
            loggedInStatusRequired === LOGGED_IN_STATUS_ALLOWED_LOGGED_IN &&
            rule?.businessRule
          ) {
            // perform check of businessRule for logged in user
            businessRulePassed = false;
            if (isLargeUser) {
              // // large user has too many products to make account check useful
            } else {
              // check the active products required by the rule
              switch (rule?.businessRule) {
                case amBusinessRules.UPP_WP_BASIC_EXISTING_CUSTOMER:
                  // User is logged in.
                  // Does not have any Active Hosting or Active eComm products,
                  // OR Has > = 1 Active Hosting or eComm products,
                  // and has not purchased any Hosting or eComm products in the last 7 days.
                  if (
                    (activeHosting === 0 && activeEcommHosting === 0) ||
                    ((activeHosting >= 1 || activeEcommHosting >= 1) &&
                      purchHostingLast7Days === 0 &&
                      purchEcommLast7Days === 0)
                  ) {
                    businessRulePassed = true;
                  }
                  break;
                default:
              }
            }
          }

          // check 1) Cart must not contain products that  that are listed in cartShouldNotInclude
          // check 2) Does cart contain ALL the products mentioned in cartIncludesAll
          // check 3) Does cart contain ANY product mentioned in cartIncludesAny
          // check 4) if minDomainsInCart is specified, check cart domain count
          // check 5) if domainsInCartExceedIncludedSku, check cart domain count exceeds them.
          //          The rule domainsInCartExceedIncludedSku should
          //          override this isAdSkuAlreadyPresent
          // check 6) check loggedInStatusAcceptablePassed
          // check 7) if rule has businessRule specified, check the account
          isRuleSelected =
            !isProductInCartShouldNotInclude &&
            ifAllProductsMatchTheRule &&
            isAnyProductInCartIncludes &&
            isMinDomainCheckOk &&
            ((!domainsInCartExceedCheckNeeded && !isAdSkuAlreadyPresent) ||
              (domainsInCartExceedCheckNeeded && domainsInCartExceedCheckOk)) &&
            loggedInStatusAcceptablePassed &&
            businessRulePassed;

          if (isRuleSelected) {
            matchingRules.push({
              ...rule,
            });
            // Pushing the selected rule name so that we skip processing it in the next iteration
            selectedRules.push(rule.name);
          }
        }
      }
    }

    let domainsEligibleForGWS: GwsDomain[] = [];

    const domainsInAccountNotHavingGWS = allDomainsForAccount
      .map((a) => Object.assign({}, a))
      .filter((domain) => allGwsDomainsForAccount.indexOf(domain.prodInstName) < 0);

    for (let ruleCount = 0; ruleCount < matchingRules.length; ruleCount++) {
      const rule = matchingRules[ruleCount];
      if (rule.checkDomainsEligibleGWS && (configEnv as any) !== 'stg') {
        domainsEligibleForGWS = await checkEligibleForGWS(
          '',
          accountId,
          products,
          rule.maximumDomainsToCheckGWS,
          configEnv,
          brand as tBrand,
          domainsInAccountNotHavingGWS,
          'uppcart',
          rngTrace,
        );
      }
    }

    const promises = getAdsFromRules(
      matchingRules,
      xSellRequestBody,
      domainsEligibleForGWS,
      rngTrace,
      allDomainsForAccount,
    );

    // Wait for all Promises to resolve
    const resolvedResponses: (AdObj | undefined)[] = await Promise.all(promises);

    // Filter if needed
    const filteredResponses = resolvedResponses.filter((i) => Boolean(i));

    // ---- FOR BLUEHOST ----
    if (brand === 'BLUEHOST') {
      // ------- BASIC HOSTING -------
      const basicHostingIndex = filteredResponses.findIndex(
        (rule) => rule?.alias === 'CART_BASIC_HOSTING',
      );
      const basicHostingIndexV2 = filteredResponses.findIndex(
        (rule) => rule?.alias === 'CART_BASIC_HOSTING_V2',
      );
      const higherIndexBasicAd = Math.max(basicHostingIndex, basicHostingIndexV2);
      if (basicHostingIndex > -1 && basicHostingIndexV2 > -1) {
        filteredResponses.splice(higherIndexBasicAd, 1);
      }
      // ------- END Wordpress Basic (Logged-in user) -------

      // ------- Wordpress Basic (Logged-in user) -------
      const wpHostingIndex = filteredResponses.findIndex(
        (rule) => rule?.alias === 'CART_WORDPRESS_BASIC',
      );
      const wpHostingIndexV2 = filteredResponses.findIndex(
        (rule) => rule?.alias === 'CART_WORDPRESS_BASIC_V2',
      );
      const higherIndexWpHostinAd = Math.max(wpHostingIndex, wpHostingIndexV2);
      if (wpHostingIndex > -1 && wpHostingIndexV2 > -1) {
        filteredResponses.splice(higherIndexWpHostinAd, 1);
      }
      // ------- END Wordpress Basic (Logged-in user) -------

      // We are limiting the response to 3 ads for bluehost.
      if (!isCSR) filteredResponses.length = Math.min(filteredResponses.length, 3);
    }
    // ---- END FOR BLUEHOST ----
    // else if (brand === 'HOSTGATOR') {
    //   if (!isCSR) filteredResponses.length = Math.min(filteredResponses.length, 3);
    // }
    const groupedRulesResponses: (AdResponse | undefined)[] = getGroupByRule(filteredResponses);
    // Assign priority based on the sorted order
    groupedRulesResponses.forEach((item: any, index) => {
      item.adDetails.priority = index + 1;
    });

    logSuccessResponse(
      resolvedResponses,
      groupedRulesResponses,
      matchingRules,
      brand as tBrand,
      currentContainerName,
      env,
      accountId,
      rngTrace,
    );

    return {
      status: 'success',
      response: groupedRulesResponses,
    };
  } catch (err: any) {
    const products = xSellRequestBody.cart.cards.map((c) => c.products).flat();
    sfLogger.error(
      {
        eventType: EVENT_TYPES.xSellServiceUppCart_CE_4,
        message: ERROR_MESSAGES.XSellServiceError,
        errorDetails: (err as Error).message,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_39,
          brand,
          responseType,
          env,
          cartCards: products,
          accountId: accountId ?? 'not passed',
        },
      },
      rngTrace,
    );
    console.error(`${ERROR_MESSAGES.XSellServiceError}: ${err.message}`);
    throw err;
  }
}
