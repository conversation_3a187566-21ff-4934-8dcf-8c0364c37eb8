import { Env, iAemRules, iRule, tBrand, ConfigCacheResults } from 'app/models/aemRules';
import { CONFIGS, getConfigs } from '../cache/configCache';
import { AdResponse, GwsDomain, XSellRequestBody } from 'app/models';
import { sfLogger } from 'app/logger';
import { ERROR_MESSAGES, EVENT_MESSAGES, EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';
import { fgProductLite, productEligibilityResult, configEnv } from '../models/index';
import BusinessRuleHelper from '../helpers/businessRuleHelper';
import { isAdAllowedForCountry } from 'app/helpers/checkCountryRuleHelper';
import { BUSINESS_RULE_PRODUCT_KEYS as productKeys } from '../constants';
import { AM_BUSINESS_RULES as amBusinessRules } from '../constants';
import {
  getGroupByRule,
  isValidEnv,
  logSuccessResponse,
  getConfigEnv,
  getRandomizedRules,
} from '../helpers/index';
import { getUserActiveProducts } from 'app/helpers/getUserActiveProducts';
import getAdsFromRules from 'app/helpers/adFetchHelpers/getAdsFromRules';
import { isEligibilityCheckRequired } from 'app/helpers/productEligibilityHelper';
import { doesUserHaveEligibleSites } from 'app/helpers/productEligibilityHelper';
import { createDecision } from '@repo/common';
import { filterTodaysUnservedRules } from 'app/helpers/dbHelpers';
export async function xSellServiceAMWI(xSellRequestBody: XSellRequestBody, rngTrace: string) {
  let retVal: any = { ad: 'notfound' };

  const {
    brand,
    responseType,
    isLoggedIn,
    userId,
    isLargeUser,
    countryCode,
    reDirectToPage,
    isFirstLogin,
  } = xSellRequestBody;

  let { env } = xSellRequestBody;
  const currentContainerName = 'AMWI';

  // translate to correct env settings
  let configInfo: configEnv = getConfigEnv(env);
  const configEnv: string = configInfo.configEnv;
  env = configInfo.env.toLowerCase() as Env;

  try {
    if (!isValidEnv(env)) {
      console.error(ERROR_MESSAGES.envInvalid);
      sfLogger.error(
        {
          eventType: EVENT_TYPES.xSellServiceAMWi_CE_4,
          message: ERROR_MESSAGES.envInvalid,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_27,
            env,
            userId,
          },
        },
        rngTrace,
      );
      throw Error(ERROR_MESSAGES.envInvalid);
    }
    // Now fetch all AEM rules
    const configCacheResults: ConfigCacheResults = await getConfigs(env, brand as tBrand);
    let AEM_RULES: iAemRules | undefined = configCacheResults.configs;
    let matchingRules: iRule[] = [];

    if (!AEM_RULES) {
      const errors = configCacheResults.statusMessages.toString();
      sfLogger.error(
        {
          eventType: EVENT_TYPES.xSellServiceAMWi_CE_2,
          message: ERROR_MESSAGES.AemRulesLoadingFailed,
          errorDetails: errors,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_29,
            AEM_RULES,
          },
        },
        rngTrace,
      );
      throw new Error(ERROR_MESSAGES.AemRulesLoadingFailed + `: brand=${brand} : ` + errors);
    }
    const rulesCopy = structuredClone(AEM_RULES.rules);
    const allRules = getRandomizedRules(rulesCopy, currentContainerName, env);

    // filter todays unserved rules
    let todaysUnservedRules = allRules;
    let todaysServedRules = [];

    const dbResponse = await filterTodaysUnservedRules(
      allRules,
      currentContainerName,
      userId,
      brand,
      rngTrace,
    );
    todaysUnservedRules = dbResponse.adsNotServedToday;
    todaysServedRules = dbResponse.adsServedToday;

    // If wi ad has already been served today, skip serving
    if (brand === 'BLUEHOST' && todaysServedRules && todaysServedRules.length > 0) {
      sfLogger.info(
        {
          eventType: EVENT_TYPES.Success_AMWI,
          message: 'Ads already served today. Skipping ad serving.',
          customAttributes: {
            brand,
            responseType,
            userId,
            env,
          },
        },
        rngTrace,
      );

      return {
        status: 'success',
        response: [],
      };
    }

    // We need to query the active products for the user account
    // since may already be logged in
    // let customerData: any;
    let activeProducts: fgProductLite[];
    activeProducts = [];

    if (isLoggedIn && userId && !isLargeUser) {
      activeProducts = await getUserActiveProducts(
        userId,
        configEnv,
        brand,
        responseType,
        rngTrace,
      );
      // customerData = details.customerData;
    }

    const businessRulesHelper = new BusinessRuleHelper();
    businessRulesHelper.loadProductMap(activeProducts);

    // grab the counts which are loaded in previous step
    const activeDomains: number = businessRulesHelper.getProductCount(productKeys.activeDomains);
    const activePR: number = businessRulesHelper.getProductCount(productKeys.activePR);
    const activeFreeTrialPR: number = businessRulesHelper.getProductCount(
      productKeys.activeFreeTrialPR,
    );
    const activeEmail: number = businessRulesHelper.getProductCount(productKeys.activeEmail);
    const activeYoastSEOPremium: number = businessRulesHelper.getProductCount(
      productKeys.activeYoastSEOPremium,
    );
    const activeHosting: number = businessRulesHelper.getProductCount(productKeys.activeHosting);
    const activePaidSitelock: number = businessRulesHelper.getProductCount(
      productKeys.activePaidSitelock,
    );
    const activeSSL: number = businessRulesHelper.getProductCount(productKeys.activeSSL);
    const activeSharedHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeSharedHosting,
    );
    const activeWordpressSolutions: number = businessRulesHelper.getProductCount(
      productKeys.activeWordpressSolutions,
    );
    const activeEcommHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeEcommHosting,
    );
    const purchHostingLast7Days: number = businessRulesHelper.getProductCount(
      productKeys.purchHostingLast7Days,
    );
    const purchEcommLast7Days: number = businessRulesHelper.getProductCount(
      productKeys.purchEcommLast7Days,
    );
    const activeProProducts: number = businessRulesHelper.getProductCount(
      productKeys.activeProProducts,
    );
    const activeYodleCustomers: number = businessRulesHelper.getProductCount(
      productKeys.activeYodleCustomers,
    );
    const activeDEP: number = businessRulesHelper.getProductCount(productKeys.activeDEP);
    const activeBusinessDirectories: number = businessRulesHelper.getProductCount(
      productKeys.activeBusinessDirectories,
    );
    const activeWebsiteTrial: number = businessRulesHelper.getProductCount(
      productKeys.activeWebsiteTrial,
    );
    const activeTitanMail: number = businessRulesHelper.getProductCount(
      productKeys.activeTitanMail,
    );
    const activeBasicHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeBasicHosting,
    );
    const activePlusHosting: number = businessRulesHelper.getProductCount(
      productKeys.activePlusHosting,
    );
    const activeProHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeProHosting,
    );
    const activeChoicePlus: number = businessRulesHelper.getProductCount(
      productKeys.activeChoicePlus,
    );
    const activeWPCloud: number = businessRulesHelper.getProductCount(productKeys.activeWPCloud);
    const activeSolutionHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeSolutionHosting,
    );
    const activeBusinessEssential: number = businessRulesHelper.getProductCount(
      productKeys.activeBusinessEssential,
    );
    const activeOnlineMarketplace: number = businessRulesHelper.getProductCount(
      productKeys.activeOnlineMarketplace,
    );
    const activeOnlineStore: number = businessRulesHelper.getProductCount(
      productKeys.activeOnlineStore,
    );
    const activeEcommDash: number = businessRulesHelper.getProductCount(
      productKeys.activeEcommDash,
    );
    const activeWebsiteMarketing: number = businessRulesHelper.getProductCount(
      productKeys.activeWebsiteMarketing,
    );
    const activeBuilderSplus: number = businessRulesHelper.getProductCount(
      productKeys.activeBuilderSplus,
    );
    const activeHighPerformanceHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeHighPerformanceHosting,
    );
    const activeWordpressHostingNetsol: number = businessRulesHelper.getProductCount(
      productKeys.activeWordpressHostingNetsol,
    );

    // stage 1: match brand

    // stage 2: match products
    for (let i = 0; i < todaysUnservedRules.length; i++) {
      const rule = todaysUnservedRules[i];

      // We don't want additional processing if we already have 3 matching rules
      if (matchingRules.length >= 1) break;

      let isRuleSelected;

      if (
        rule.notShowInFirstLogin &&
        (isFirstLogin || isFirstLogin === null) // AM wants us to check for null as well - SOFT-163661
      ) {
        isRuleSelected = false;
      } else if (
        !isAdAllowedForCountry(
          countryCode,
          rule.actionType,
          currentContainerName,
          rule.businessRule,
        )
      ) {
        isRuleSelected = false;
      } else {
        let isBusinessRuleMatch = false;
        // check the active products required by the rule
        switch (rule?.businessRule) {
          case amBusinessRules.ACTIVE_DOMAIN_EXCEEDS_PRREG:
          case amBusinessRules.WI_PRREG:
            if (activeDomains > activePR) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ANYONE_ELIGIBLE:
            isBusinessRuleMatch = true;
            break;
          case amBusinessRules.ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL:
          case amBusinessRules.WI_GOOGLE_WORKSPACE:
          case amBusinessRules.WI_PRO_EMAIL:
            if (activeDomains > activeEmail) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL_NO_TITAN:
          case amBusinessRules.WI_GOOGLE_WORKSPACE_NO_TITAN:
            // if the account has titan dont allow the ad
            if (activeDomains > activeEmail && activeTitanMail === 0) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_HOSTING_EXCEEDS_PAID_SITELOCK:
          case amBusinessRules.WI_SITELOCK:
            if (activeHosting > activePaidSitelock) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_HOSTING_EXCEEDS_YOAST_SEO_PREMIUM:
          case amBusinessRules.WI_YOAST_SEO_PREMIUM:
            if (activeHosting > activeYoastSEOPremium && activeWPCloud === 0) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_DOMAIN_EXCEEDS_ACTIVE_SSLCERT:
          case amBusinessRules.WI_SSL_FAMILY:
            if (activeDomains > activeSSL) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.CUST_HAS_ACTIVE_SHARED_HOSTING_AND_CLOUD_HOSTING_WITHOUT_OTHERS:
          case amBusinessRules.WI_WP_SOLUTION_FAMILY:
            // check basic, plus, pro, choice plus, wp cloud hosting
            // and not allowed others :
            // * Does not have any WP Solutions packages
            // * Does not have any active eCommerce products
            // * No active Solution Hosting
            if (
              (activeBasicHosting > 0 ||
                activePlusHosting > 0 ||
                activeProHosting > 0 ||
                activeChoicePlus > 0 ||
                activeWPCloud > 0 ||
                activeHighPerformanceHosting > 0) &&
              activeWordpressSolutions === 0 &&
              activeEcommHosting === 0 &&
              activeSolutionHosting === 0
            ) {
              isBusinessRuleMatch = true;
            }

            break;
          case amBusinessRules.WI_WEBSITE_GRADER:
            if (
              activeEcommDash > 0 ||
              activeBusinessEssential > 0 ||
              activeWebsiteMarketing > 0 ||
              activeOnlineStore > 0 ||
              activeOnlineMarketplace > 0 ||
              activeBuilderSplus > 0 ||
              activeSharedHosting > 0 ||
              activeWordpressHostingNetsol > 0
            ) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_HOSTING_OR_ECOMM_NO_PURCH_LAST_7:
          case amBusinessRules.WI_HOSTING_FAMILY_HAS_ACTIVE:
            if (
              (activeHosting > 0 || activeEcommHosting > 0) &&
              purchHostingLast7Days === 0 &&
              purchEcommLast7Days === 0
            ) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.NO_ACTIVE_HOSTING_OR_ECOMM:
          case amBusinessRules.WI_HOSTING_FAMILY_NO_ACTIVE:
            if (activeHosting === 0 && activeEcommHosting === 0) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.NO_ECOMDASH_OR_BUSESSENTIAL_OR_WEBMKTG_OR_ONLINE_STORE_MKT:
          case amBusinessRules.WI_ECOMMDASH_MYSCHEDULER:
            // * Customer does not have any of the following SKUs:
            // * ECOMDASH_, BUSINESS_ESSENTIAL, WEBSITE_MARKETING, ONLINE_STORE, ONLINE_MARKETPLACE
            // * exclude if has WEBSITE and WEBSITE_L (builder s+)
            if (
              activeBusinessEssential === 0 &&
              activeOnlineMarketplace === 0 &&
              activeOnlineStore === 0 &&
              activeEcommDash === 0 &&
              activeWebsiteMarketing === 0 &&
              activeBuilderSplus === 0
            ) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.WI_PRO_WEBSITE:
            // Must have Shared Hosting in Account or ECommerce Hosting
            if (activeSharedHosting > 0 || activeEcommHosting > 0) {
              isBusinessRuleMatch = true;
            }

            break;
          default:
        }

        // check 1) if isBusinessRuleMatch
        isRuleSelected = isBusinessRuleMatch;

        if (isRuleSelected) {
          matchingRules.push({
            ...rule,
          });
        }
      }
    }

    // GWS eligibility
    let domainsEligibleForGWS: GwsDomain[] = [];

    // Fetch all domains from the account if user is logged in
    let allDomainsForUser: fgProductLite[] = businessRulesHelper.getDomainsForUser();
    let allGwsDomainsForUser: string[] = businessRulesHelper.getGwsDomainsForUser();
    const domainsInAccountNotHavingGWS = allDomainsForUser
      .map((a) => Object.assign({}, a))
      .filter((domain) => allGwsDomainsForUser.indexOf(domain.prodInstName) < 0);

    const promises = getAdsFromRules(
      matchingRules,
      xSellRequestBody,
      domainsEligibleForGWS,
      rngTrace,
      allDomainsForUser,
    );

    // Wait for all Promises to resolve
    const resolvedResponses = await Promise.all(promises);

    // Filter if needed
    const filteredResponses = resolvedResponses.filter((i) => Boolean(i));

    const groupedRulesResponses = getGroupByRule(filteredResponses);

    let statusDetails = '';

    // for some sku we moved eligibility checking here to avoid
    // having to do it on account manager after ads are returned
    if (groupedRulesResponses.length > 0) {
      const firstAd: any = groupedRulesResponses[0];
      const sku = firstAd?.adDetails?.sku;
      const isEligCheckRequired = isEligibilityCheckRequired(sku);
      if (isEligCheckRequired) {
        let isEligibilityCheckPassed = false;
        if (!isLargeUser) {
          const hostingForUser = businessRulesHelper.getHostingForUser();
          let productEligResult: productEligibilityResult = await doesUserHaveEligibleSites(
            configEnv,
            brand,
            userId,
            hostingForUser,
            sku,
            rngTrace,
          );
          isEligibilityCheckPassed = productEligResult?.isEligibilityCheckPassed;
          statusDetails = productEligResult?.statusMessage;
        }

        if (!isEligibilityCheckPassed) {
          // remove the ad which didnt pass eligibility check
          groupedRulesResponses.shift();
        }
      }
    }

    if (groupedRulesResponses.length > 1) {
      groupedRulesResponses.length = 1; // Limit to 1 ad response for wi
    }

    // Assign priority based on the sorted order
    groupedRulesResponses.forEach((item: any, index) => {
      item.adDetails.priority = index + 1;
    });

    logSuccessResponse(
      resolvedResponses,
      groupedRulesResponses,
      matchingRules,
      brand as tBrand,
      currentContainerName,
      env,
      userId,
      rngTrace,
    );

    try {
      // Logging data to decisions table
      const createDecisionPromises = groupedRulesResponses.map(
        async (r: AdResponse | undefined) => {
          return createDecision({
            brand: brand,
            surface: 'AMWI',
            customerId: Number(userId),
            adIdentifier: r?.adDetails.adIdentifier || '',
          });
        },
      );
      await Promise.all(createDecisionPromises);
    } catch (error) {
      sfLogger.error(
        {
          eventType: EVENT_TYPES.PushDecisionsToDB_Fail,
          message: ERROR_MESSAGES.PushDecisionsToDBFailed,
          errorDetails: (error as Error).message,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_52,
            surface: currentContainerName,
            brand,
            responseType,
            groupedRulesResponses,
            userId,
          },
        },
        rngTrace,
      );
    }

    return {
      status: 'success',
      response: groupedRulesResponses,
      statusDetails,
    };
  } catch (err: any) {
    // Ignoring this error as it's a recurring fg API issue, not caused by our service. So we log it as info.
    if (
      err.message?.includes('com.netsol.workflow.framework.CWIException: No data found in EDB.')
    ) {
      sfLogger.info(
        {
          eventType: 'Account not found',
          message: (err as Error).message,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_33,
            brand,
            responseType,
            userId,
            env,
            surface: currentContainerName,
          },
        },
        rngTrace,
      );
    } else {
      sfLogger.error(
        {
          eventType: EVENT_TYPES.xSellServiceAMWi_CE_3,
          message: ERROR_MESSAGES.XSellServiceAMWiError,
          errorDetails: (err as Error).message,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_33,
            brand,
            responseType,
            userId,
            env,
          },
        },
        rngTrace,
      );
    }
    console.log(`${ERROR_MESSAGES.XSellServiceAMWiError}: ${err.message}`);
    throw err;
  }
  return retVal;
}
