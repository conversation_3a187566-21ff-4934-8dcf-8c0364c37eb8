import { CONFIGS, getConfigs } from 'app/cache/configCache';
import { ERROR_MESSAGES, EVENT_MESSAGES, EVENT_TYPES, PROD, SFLOGGER_IDS } from 'app/constants';
import { getGroupByRule, logSuccessResponse, getConfigEnv } from 'app/helpers';
import getAdsFromRules from 'app/helpers/adFetchHelpers/getAdsFromRules';
import { sfLogger } from 'app/logger';
import { XSellRequestBody } from 'app/models';
import { Env, iAemRules, iRule, tBrand, ConfigCacheResults } from 'app/models/aemRules';
import { configEnv } from '../models/index';

export async function xSellServiceTestOffers(
  xSellRequestBody: XSellRequestBody,
  containerName: string,
  rngTrace: string,
) {
  const { brand, responseType, testOffers, isLoggedIn, userId, isLargeUser } = xSellRequestBody;
  let { env } = xSellRequestBody;
  const currentContainerName = 'TestOffers';

  // translate to correct env settings
  let configInfo: configEnv = getConfigEnv(env);
  const configEnv: string = configInfo.configEnv;
  env = configInfo.env.toLowerCase() as Env;

  // Now fetch all AEM rules
  const configCacheResults: ConfigCacheResults = await getConfigs(env, brand as tBrand);
  let AEM_RULES: iAemRules | undefined = configCacheResults.configs;
  let matchingRules: iRule[] = [];

  if (!AEM_RULES) {
    const errors = configCacheResults.statusMessages.toString();
    sfLogger.error(
      {
        eventType: EVENT_TYPES.xSellServiceAMTestOffers,
        message: ERROR_MESSAGES.AemRulesLoadingFailed,
        errorDetails: errors,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_43,
          AEM_RULES,
          userId,
        },
      },
      rngTrace,
    );
    throw new Error(ERROR_MESSAGES.AemRulesLoadingFailed + `: brand=${brand} : ` + errors);
  }
  try {
    const offers = testOffers.map((offerInArray: string) => {
      return AEM_RULES.rules.find((rule) => {
        return (
          (rule.businessRule === offerInArray || rule?.alias === offerInArray) &&
          rule.containerName === containerName
        );
      });
    });
    const rules: iRule[] = offers.filter((rule): rule is iRule => rule !== undefined);

    // We can use structuredClone(rule) for the deep copy as well (if the rule object becomes complex).
    matchingRules = rules.map((rule) => Object.assign({}, rule));

    const promises = getAdsFromRules(matchingRules, xSellRequestBody, [], rngTrace, []);

    // Wait for all Promises to resolve
    const resolvedResponses = await Promise.all(promises);

    // Filter if needed
    const filteredResponses = resolvedResponses.filter((i) => Boolean(i));
    const groupedRulesResponses = getGroupByRule(filteredResponses);

    return {
      status: 'success',
      response: groupedRulesResponses,
    };
  } catch (err: any) {
    sfLogger.error(
      {
        eventType: EVENT_TYPES.xSellServiceAMTestOffers,
        message: ERROR_MESSAGES.XSellServiceAMTestOffersError,
        errorDetails: (err as Error).message,
        customAttributes: {
          initiator: SFLOGGER_IDS.SFLOG_ID_33,
          brand,
          responseType,
          userId,
        },
      },
      rngTrace,
    );
    console.log(`${ERROR_MESSAGES.XSellServiceAMTestOffersError}: ${err.message}`);
    throw err;
  }
}
