import { Env, iAemRules, iRule, tBrand, ConfigCacheResults } from 'app/models/aemRules';
import { CONFIGS, getConfigs } from '../cache/configCache';
import { GwsDomain, XSellRequestBody } from 'app/models';
import { isLifecycleActive } from '../helpers/productIdentifiers';
import { sfLogger } from 'app/logger';
import { ERROR_MESSAGES, EVENT_TYPES, SFLOGGER_IDS } from 'app/constants';
import { getProductsForSingleAccount, getAccountsForUser } from './userAccountQueryService';
import { fgProductLite, productEligibilityResult, configEnv } from '../models/index';
import BusinessRuleHelper from '../helpers/businessRuleHelper';
import { BUSINESS_RULE_PRODUCT_KEYS as productKeys } from '../constants';
import { AM_BUSINESS_RULES as amBusinessRules } from '../constants';
import { checkEligibleForGWS } from 'app/helpers/checkGWSInfo';
import getAdsFromRules from 'app/helpers/adFetchHelpers/getAdsFromRules';
import { getGroupByRule, logSuccessResponse, getConfigEnv, getRandomizedRules } from 'app/helpers';
import { isEligibilityCheckRequired } from 'app/helpers/productEligibilityHelper';
import { doesUserHaveEligibleSites } from 'app/helpers/productEligibilityHelper';
import { isAdAllowedForCountry } from '../helpers/checkCountryRuleHelper';

export async function xSellServiceAMHPCards(xSellRequestBody: XSellRequestBody, rngTrace: string) {
  let retVal: any = { ad: 'notfound' };

  const {
    brand,
    responseType,
    isLoggedIn,
    userId,
    isLargeUser,
    countryCode,
    reDirectToPage,
    isFirstLogin,
  } = xSellRequestBody;

  let { env } = xSellRequestBody;
  const currentContainerName = 'AMHPCards';
  const currencyCode = xSellRequestBody?.currencyCode || 'USD';

  // translate to correct env settings
  let configInfo: configEnv = getConfigEnv(env);
  const configEnv: string = configInfo.configEnv;
  env = configInfo.env.toLowerCase() as Env;

  try {
    if (!env) {
      throw Error('The env param (qa | prod) was not passed in the request and is required');
    }
    // Now fetch all AEM rules
    const configCacheResults: ConfigCacheResults = await getConfigs(env, brand as tBrand);
    let AEM_RULES: iAemRules | undefined = configCacheResults.configs;
    let matchingRules: iRule[] = [];

    if (!AEM_RULES) {
      const errors = configCacheResults.statusMessages.toString();
      sfLogger.error(
        {
          eventType: EVENT_TYPES.xSellServiceAMHP_CE_2,
          message: ERROR_MESSAGES.AemRulesLoadingFailed,
          errorDetails: errors,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_19,
            AEM_RULES,
          },
        },
        rngTrace,
      );
      throw new Error(ERROR_MESSAGES.AemRulesLoadingFailed + `: brand=${brand} : ` + errors);
    }
    const rulesCopy = structuredClone(AEM_RULES.rules);
    const allRules = getRandomizedRules(rulesCopy, currentContainerName, env);

    // We need to query the active products for the user account
    // since may already be logged in
    let customerData: any;
    let activeProducts: fgProductLite[];
    activeProducts = [];

    if (isLoggedIn && userId && !isLargeUser) {
      const accountsResponseData = await getAccountsForUser(configEnv, userId);

      let {
        status: accountQueryStatus,
        errorCode: accountQueryErrorCode,
        errMessage: accountQueryErrMessage,
      } = accountsResponseData;

      if (accountQueryStatus === 'error') {
        sfLogger.error(
          {
            eventType: EVENT_TYPES.xSellServiceAMHP_QueryAccounts_Fail,
            message: ERROR_MESSAGES.QueryAccountsFailed,
            customAttributes: {
              initiator: SFLOGGER_IDS.SFLOG_ID_20,
              userId,
              accountQueryErrMessage,
              accountQueryErrorCode,
            },
          },
          rngTrace,
        );
        throw new Error(
          `Problem querying the accounts for the userId ${userId}: ${accountQueryErrMessage}`,
        );
      }

      //console.log(`accountsResponseData = ${JSON.stringify(accountsResponseData, null, '\t')}`);

      //query the products for each account, combine, and apply rules
      for (let i = 0; i < accountsResponseData.data.userAccounts?.length; i++) {
        const { accountId } = accountsResponseData.data.userAccounts[i];
        let activeProductsForAccount = [];
        // query the products in the user acct
        try {
          customerData = await getProductsForSingleAccount(configEnv, accountId);
          let {
            status: productQueryStatus,
            errorCode: productQueryErrorCode,
            errMessage: productQueryErrorMessage,
          } = customerData;

          if (productQueryStatus === 'error') {
            sfLogger.error(
              {
                eventType: EVENT_TYPES.xSellServiceAMHP_QueryProducts_Fail,
                message: ERROR_MESSAGES.QueryProductsFailed,
                customAttributes: {
                  initiator: SFLOGGER_IDS.SFLOG_ID_21,
                  accountId,
                  productQueryErrorMessage,
                  productQueryErrorCode,
                },
              },
              rngTrace,
            );
            throw new Error(
              `xSellServiceAMHPCards() - problem querying the products for account ${accountId}: ${productQueryErrorMessage}`,
            );
          }

          if (customerData && productQueryStatus === 'success') {
            //locate active products by their lifecycleCdId
            for (let j = 0; j < customerData.data.products?.length; j++) {
              if (isLifecycleActive(customerData.data.products[j].lifecycleCdId)) {
                activeProductsForAccount.push(customerData.data.products[j]);
              }
            }

            if (activeProductsForAccount?.length) {
              activeProducts = activeProducts.concat(activeProductsForAccount);
            }
          }
        } catch (err: any) {
          sfLogger.error(
            {
              eventType: EVENT_TYPES.xSellServiceAMHP_CE_1,
              message: ERROR_MESSAGES.XSellServiceAMHPError,
              errorDetails: (err as Error).message,
              customAttributes: {
                initiator: SFLOGGER_IDS.SFLOG_ID_22,
                brand,
                userId,
                responseType,
              },
            },
            rngTrace,
          );
          const errorPrefix =
            'Error returned to xSellServiceAMHPCards during call to get products for account';
          if (err.message) {
            console.error(`${errorPrefix} ${accountId} : ${err.message}`);
          } else {
            console.error(`${errorPrefix} ${accountId} : ${JSON.stringify(err, null, '\t')}`);
          }
        }
      }
    } else {
      console.log(`user is not logged in`);
    }

    const businessRulesHelper = new BusinessRuleHelper();
    businessRulesHelper.loadProductMap(activeProducts);

    // grab the counts which are loaded in previous step
    const activeDomains: number = businessRulesHelper.getProductCount(productKeys.activeDomains);
    const activePR: number = businessRulesHelper.getProductCount(productKeys.activePR);
    const activeFreeTrialPR: number = businessRulesHelper.getProductCount(
      productKeys.activeFreeTrialPR,
    );
    const activeEmail: number = businessRulesHelper.getProductCount(productKeys.activeEmail);
    const activeYoastSEOPremium: number = businessRulesHelper.getProductCount(
      productKeys.activeYoastSEOPremium,
    );
    const activeHosting: number = businessRulesHelper.getProductCount(productKeys.activeHosting);
    const activePaidSitelock: number = businessRulesHelper.getProductCount(
      productKeys.activePaidSitelock,
    );
    const activeSSL: number = businessRulesHelper.getProductCount(productKeys.activeSSL);
    const activeSharedHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeSharedHosting,
    );
    const activeWordpressSolutions: number = businessRulesHelper.getProductCount(
      productKeys.activeWordpressSolutions,
    );
    const activeEcommHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeEcommHosting,
    );
    const purchHostingLast7Days: number = businessRulesHelper.getProductCount(
      productKeys.purchHostingLast7Days,
    );
    const purchEcommLast7Days: number = businessRulesHelper.getProductCount(
      productKeys.purchEcommLast7Days,
    );
    const activeProProducts: number = businessRulesHelper.getProductCount(
      productKeys.activeProProducts,
    );
    const activeYodleCustomers: number = businessRulesHelper.getProductCount(
      productKeys.activeYodleCustomers,
    );
    const activeDEP: number = businessRulesHelper.getProductCount(productKeys.activeDEP);
    const activeBusinessDirectories: number = businessRulesHelper.getProductCount(
      productKeys.activeBusinessDirectories,
    );
    const activeWebsiteTrial: number = businessRulesHelper.getProductCount(
      productKeys.activeWebsiteTrial,
    );
    const activeBasicHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeBasicHosting,
    );
    const activeChoicePlusHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeChoicePlus,
    );
    const activeProHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeProHosting,
    );
    const activeBuilderProducts: number = businessRulesHelper.getProductCount(
      productKeys.activeBuilder,
    );
    const activeTitanMail: number = businessRulesHelper.getProductCount(
      productKeys.activeTitanMail,
    );
    const activePlusHosting: number = businessRulesHelper.getProductCount(
      productKeys.activePlusHosting,
    );
    const activeWPCloud: number = businessRulesHelper.getProductCount(productKeys.activeWPCloud);
    const activeSolutionHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeSolutionHosting,
    );
    const activeBusinessEssential: number = businessRulesHelper.getProductCount(
      productKeys.activeBusinessEssential,
    );
    const activeOnlineMarketplace: number = businessRulesHelper.getProductCount(
      productKeys.activeOnlineMarketplace,
    );
    const activeOnlineStore: number = businessRulesHelper.getProductCount(
      productKeys.activeOnlineStore,
    );
    const activeEcommDash: number = businessRulesHelper.getProductCount(
      productKeys.activeEcommDash,
    );
    const activeWebsiteMarketing: number = businessRulesHelper.getProductCount(
      productKeys.activeWebsiteMarketing,
    );
    const activeDIFMHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeDIFMHosting,
    );
    const activeProOnlineStore: number = businessRulesHelper.getProductCount(
      productKeys.activeProOnlineStore,
    );
    const activeProWebsite: number = businessRulesHelper.getProductCount(
      productKeys.activeProWebsite,
    );
    const activeBuilderSplus: number = businessRulesHelper.getProductCount(
      productKeys.activeBuilderSplus,
    );
    const activeHighPerformanceHosting: number = businessRulesHelper.getProductCount(
      productKeys.activeHighPerformanceHosting,
    );

    // stage 1: match brand

    // stage 2: match products

    for (let i = 0; i < allRules.length; i++) {
      const rule = allRules[i];
      // We don't want additional processing if we already have 3 matching rules
      if (matchingRules.length >= 2) break;

      let isRuleSelected;
      if (
        rule.notShowInFirstLogin &&
        (isFirstLogin || isFirstLogin === null) // AM wants us to check for null as well - SOFT-163661
      ) {
        isRuleSelected = false;
      } else if (
        !isAdAllowedForCountry(
          countryCode,
          rule.actionType,
          currentContainerName,
          rule.businessRule,
        )
      ) {
        isRuleSelected = false;
      } else {
        let isBusinessRuleMatch = false;
        // check the active products required by the rule
        switch (rule?.businessRule) {
          case amBusinessRules.ACTIVE_DOMAIN_EXCEEDS_PRREG:
          case amBusinessRules.HOME_PRREG:
            if (activeDomains > activePR) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL:
          case amBusinessRules.HOME_GOOGLE_WORKSPACE:
          case amBusinessRules.HOME_PRO_EMAIL:
            if (activeDomains > activeEmail) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL_NO_TITAN:
          case amBusinessRules.HOME_GOOGLE_WORKSPACE_NO_TITAN:
            // if the account has titan dont allow the ad.
            if (activeDomains > activeEmail && activeTitanMail === 0) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_HOSTING_EXCEEDS_PAID_SITELOCK:
          case amBusinessRules.HOME_SITELOCK:
            if (activeHosting > activePaidSitelock) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_HOSTING_EXCEEDS_YOAST_SEO_PREMIUM:
          case amBusinessRules.HOME_YOAST_SEO_PREMIUM:
            if (activeHosting > activeYoastSEOPremium && activeWPCloud === 0) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ACTIVE_DOMAIN_EXCEEDS_ACTIVE_SSLCERT:
          case amBusinessRules.HOME_SSL_FAMILY:
            if (activeDomains > activeSSL) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.CUST_HAS_ACTIVE_SHARED_HOSTING_WITHOUT_OTHERS:
          case amBusinessRules.HOME_WP_SOLUTION_FAMILY:
            // check for shared hosting
            // and not allowed others :
            // * Does not have any WP Solutions packages
            // * Does not have any active eCommerce products
            if (
              activeSharedHosting > 0 &&
              activeWordpressSolutions === 0 &&
              activeEcommHosting === 0
            ) {
              isBusinessRuleMatch = true;
            }

            break;
          case amBusinessRules.ACTIVE_HOSTING_OR_ECOMM_NO_PURCH_LAST_7:
          case amBusinessRules.HOME_HOSTING_FAMILY_HAS_ACTIVE:
            if (
              (activeHosting > 0 || activeEcommHosting > 0) &&
              purchHostingLast7Days === 0 &&
              purchEcommLast7Days === 0
            ) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.NO_ACTIVE_HOSTING_OR_ECOMM:
          case amBusinessRules.HOME_HOSTING_FAMILY_NO_ACTIVE:
            if (activeHosting === 0 && activeEcommHosting === 0) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.BASIC_OR_PLUS_HOSTING_NO_CHOICEPLUS_OR_PRO_OR_WPSOLUTIONS_OR_ECOMM:
          case amBusinessRules.HOME_SOLUTION_CREATOR:
            //Customer has one or more of the following active Hosting products:
            // BH_PKG_WP_ENT, BH_PKG_WP_PLUS
            // No active Choice Plus or Pro Hosting products
            // No active WP Solutions or eCommerce
            // No active Solution Hosting
            if (
              (activeBasicHosting >= 1 || activePlusHosting >= 1) &&
              activeChoicePlusHosting === 0 &&
              activeProHosting === 0 &&
              activeWordpressSolutions === 0 &&
              activeEcommHosting === 0 &&
              activeSolutionHosting === 0 &&
              activeHighPerformanceHosting === 0
            ) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.VARIOUS_HOSTING_NO_BASIC_OR_WPSOLUTIONS_OR_ECOMM:
          case amBusinessRules.HOME_SOLUTION_COMMERCE:
            //Customer has one or more of the following active Hosting products:
            // BH_PKG_WP_PRO, BH_PKG_WP_CHOICE_PLUS, WP_CLOUD_1,
            // WP_CLOUD_10,WP_CLOUD_25,WP_CLOUD_50
            // No active Basic Hosting, WP Solutions, No eCommerce
            // No active Solution Hosting
            if (
              (activeChoicePlusHosting >= 1 ||
                activeProHosting >= 1 ||
                activeWPCloud >= 1 ||
                activeHighPerformanceHosting >= 1) &&
              activeBasicHosting === 0 &&
              activeWordpressSolutions === 0 &&
              activeEcommHosting === 0 &&
              activeSolutionHosting === 0
            ) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.HOME_WEBSITE_GRADER:
            if (
              (countryCode === 'US' || countryCode === 'CA') &&
              activeYodleCustomers === 0 &&
              activeDIFMHosting === 0 &&
              activeProOnlineStore === 0 &&
              activeProWebsite === 0
            ) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.HOME_DOM_ALT:
            if (activeDomains > 0) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.HOME_ECOMMDASH_MYSCHEDULER:
            // * Customer does not have any of the following SKUs:
            // * ECOMDASH_, BUSINESS_ESSENTIAL, WEBSITE_MARKETING, ONLINE_STORE, ONLINE_MARKETPLACE
            // * exclude if has WEBSITE and WEBSITE_L (builder s+)
            if (
              activeBusinessEssential === 0 &&
              activeOnlineMarketplace === 0 &&
              activeOnlineStore === 0 &&
              activeEcommDash === 0 &&
              activeWebsiteMarketing === 0 &&
              activeBuilderSplus === 0
            ) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.ANYONE_ELIGIBLE:
          case amBusinessRules.HOME_DOMAIN_TRANSFER:
          case amBusinessRules.HOME_PRO_WEBSITE:
            isBusinessRuleMatch = true;
            break;
          case amBusinessRules.PRO_WEBSITE500OFF_AMCARD_TEXT_ONLY:
          case amBusinessRules.HOME_PRO_WEBSITE500OFF_AMCARD_TEXT_ONLY:
            if (
              activeDomains > 0 &&
              (countryCode === 'US' || countryCode === 'CA') &&
              activeYodleCustomers === 0
            ) {
              isBusinessRuleMatch = true;
            }
            break;
          case amBusinessRules.KBSEARCH:
          case amBusinessRules.HOME_KBSEARCH:
            isBusinessRuleMatch = true;
            break;
          default:
        }

        // check 1) if isBusinessRuleMatch
        isRuleSelected = isBusinessRuleMatch;

        if (isRuleSelected) {
          matchingRules.push({
            ...rule,
          });
        }
      }
    }
    // GWS eligibility
    let domainsEligibleForGWS: GwsDomain[] = [];

    // Fetch all domains from the account if user is logged in
    let allDomainsForUser: fgProductLite[] = businessRulesHelper.getDomainsForUser();
    let allGwsDomainsForUser: string[] = businessRulesHelper.getGwsDomainsForUser();
    const domainsInAccountNotWithGWS = allDomainsForUser
      .map((a) => Object.assign({}, a))
      .filter((domain) => allGwsDomainsForUser.indexOf(domain.prodInstName) < 0);

    const promises = getAdsFromRules(
      matchingRules,
      xSellRequestBody,
      domainsInAccountNotWithGWS,
      rngTrace,
      allDomainsForUser,
    );

    // Wait for all Promises to resolve
    const resolvedResponses = await Promise.all(promises);

    // Filter if needed
    const filteredResponses = resolvedResponses.filter((i) => Boolean(i));

    const groupedRulesResponses = getGroupByRule(filteredResponses);

    let statusDetails = '';

    // for some sku we moved eligibility checking here to avoid
    // having to do it on account manager after ads are returned
    if (groupedRulesResponses.length > 0) {
      let limit = groupedRulesResponses.length;
      // only do check for top 3 ads to show on client
      if (limit > 3) {
        limit = 3;
      }

      for (let i = limit - 1; i >= 0; i--) {
        let isEligCheckRequired = false;
        const currrentAd: any = groupedRulesResponses[i];
        const sku = currrentAd?.adDetails?.sku;
        isEligCheckRequired = isEligibilityCheckRequired(sku);
        if (isEligCheckRequired) {
          let isEligibilityCheckPassed = false;
          if (!isLargeUser) {
            const hostingForUser = businessRulesHelper.getHostingForUser();
            let productEligResult: productEligibilityResult = await doesUserHaveEligibleSites(
              configEnv,
              brand,
              userId,
              hostingForUser,
              sku,
              rngTrace,
            );
            isEligibilityCheckPassed = productEligResult?.isEligibilityCheckPassed;
            statusDetails = productEligResult?.statusMessage;
          }

          if (!isEligibilityCheckPassed) {
            // remove the ad which didnt pass eligibility check
            groupedRulesResponses.splice(i, 1);
          }
        }
      }
    }

    // Assign priority based on the sorted order
    groupedRulesResponses.forEach((item: any, index) => {
      item.adDetails.priority = index + 1;
    });
    logSuccessResponse(
      resolvedResponses,
      groupedRulesResponses,
      matchingRules,
      brand as tBrand,
      currentContainerName,
      env,
      userId,
      rngTrace,
    );
    return {
      status: 'success',
      response: groupedRulesResponses,
      statusDetails,
    };
  } catch (err: any) {
    // Ignoring this error as it's a recurring fg API issue, not caused by our service. So we log it as info.
    if (
      err.message?.includes('com.netsol.workflow.framework.CWIException: No data found in EDB.')
    ) {
      sfLogger.info(
        {
          eventType: 'Account not found',
          message: (err as Error).message,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_26,
            brand,
            responseType,
            userId,
            env,
            surface: currentContainerName,
          },
        },
        rngTrace,
      );
      return {
        status: 'success',
        response: [],
      };
    } else {
      sfLogger.error(
        {
          eventType: EVENT_TYPES.xSellServiceAMHP_CE_3,
          message: ERROR_MESSAGES.XSellServiceAMHPError,
          errorDetails: (err as Error).message,
          customAttributes: {
            initiator: SFLOGGER_IDS.SFLOG_ID_26,
            brand,
            responseType,
            userId,
            env,
          },
        },
        rngTrace,
      );
    }
    console.log(`${ERROR_MESSAGES.XSellServiceAMHPError}: ${err.message}`);
    throw err;
  }
}
