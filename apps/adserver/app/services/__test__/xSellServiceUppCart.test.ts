import request from 'supertest';
import app from '../../index';
import defaultPayloadJson from './data/default-payload.json';
import { CONFIGS } from 'app/cache/configCache';
import bluehostRules from './data/uppcart-qa-bluehost-rules.json';
import { sfLogger } from 'app/logger';

const defaultPayload = JSON.parse(JSON.stringify(defaultPayloadJson));

beforeAll(() => {
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
  jest.spyOn(CONFIGS.qa.BLUEHOST, 'getConfig').mockResolvedValue(bluehostRules as any);
  sfLogger.error = jest.fn();
  sfLogger.info = jest.fn();
});

describe('xSellServiceUppCart', () => {
  it('1. should return correct ads for "cartIncludesAny" and "cartShouldNotInclude"', async () => {
    defaultPayload.cart.cards[0].products = [
      { productCode: 'BH_PKG_WP_ENT', productType: 'SFHosting' },
      { productCode: 'WP_SOLUTION_CREATOR', productType: 'SFHosting' },
      { productCode: 'SITELOCK_ESSENTIALS', productType: 'SFHosting' },
    ];

    const apiRes = await request(app).post('/api/v1/getXSell').send(defaultPayload);
    const ads = JSON.parse(apiRes.text).response;
    const adSkus = ads.map((ad: any) => ad.adDetails.productSku);

    expect(ads).toHaveLength(1);
    expect(adSkus).toEqual(['CODEGUARD_BASIC_V2']);
  });

  it('2. should return correct ads for "cartIncludesAll"', async () => {
    defaultPayload.cart.cards[0].products = [
      { productCode: 'TESTING_SKU_0', productType: 'SFHosting' },
      { productCode: 'TESTING_SKU_1', productType: 'SFHosting' },
    ];

    const apiRes = await request(app).post('/api/v1/getXSell').send(defaultPayload);
    const ads = JSON.parse(apiRes.text).response;
    const adSkus = ads.map((ad: any) => ad.adDetails.productSku);

    expect(ads).toHaveLength(1);
    expect(adSkus).toEqual(['TESTING_SKU_3']);
  });

  it('4. Determine GWS eligibility based on cart', async () => {
    defaultPayload.accountId = 'nonExistentAccountId';
    defaultPayload.cart.cards[0].products = [
      {
        productCode: 'BH_PKG_WP_ENT',
        productType: 'SFHosting',
      },
      {
        productCode: 'CODEGUARD_BASIC_V2',
        productType: 'SFHosting',
      },
      {
        productCode: 'SITELOCK_ESSENTIALS',
        productType: 'SFHosting',
      },
      {
        productCode: 'WP_SOLUTION_CREATOR',
        productType: 'SFHosting',
      },
      {
        productCode: 'DOM_COM',
        productType: 'SFDomain',
        domainName: 'bluecat.com',
      },
      {
        productCode: 'DOM_COM',
        productType: 'SFDomain',
        domainName: 'greendog.com',
      },
    ];

    const xSellResponse = await request(app).post('/api/v1/getXSell').send(defaultPayload);
    const ads = JSON.parse(xSellResponse.text).response;

    expect(ads).toHaveLength(1);
    expect(ads[0].adDetails.sku).toBe('GOOGLE_WORKSPACE_STARTER');
    expect(ads[0].adDetails.eligibleDomainsList).toHaveLength(2);
    expect(ads[0].adDetails.eligibleDomainsList).toEqual([
      {
        prodInstName: 'bluecat.com',
      },
      {
        prodInstName: 'greendog.com',
      },
    ]);
  });

  it('5. Determine GWS eligibility based on active products', async () => {
    defaultPayload.accountId = *********; // I created this account exclusively for testing adServer
    defaultPayload.cart.cards[0].products = [
      {
        productCode: 'BH_PKG_WP_ENT',
        productType: 'SFHosting',
      },
      {
        productCode: 'CODEGUARD_BASIC_V2',
        productType: 'SFHosting',
      },
      {
        productCode: 'SITELOCK_ESSENTIALS',
        productType: 'SFHosting',
      },
      {
        productCode: 'WP_SOLUTION_CREATOR',
        productType: 'SFHosting',
      },
      {
        domainName: 'squaredbird.com',
        productType: 'SFDomain',
      },
    ];

    const xSellResponse = await request(app).post('/api/v1/getXSell').send(defaultPayload);
    const ads = JSON.parse(xSellResponse.text).response;

    expect(ads).toHaveLength(1);
    expect(ads[0].adDetails.sku).toBe('GOOGLE_WORKSPACE_STARTER');
    expect(ads[0].adDetails.eligibleDomainsList).toHaveLength(3);
    expect(ads[0].adDetails.eligibleDomainsList).toEqual([
      {
        prodInstName: 'squaredbird.com',
      },
      {
        prodInstName: 'SUGARCLOUDS9.CO',
        prodInstId: 'WN.D.*********',
        accountId: *********,
      },
      {
        prodInstName: 'SUGARCLOUDS9.COM',
        prodInstId: 'WN.D.*********',
        accountId: *********,
      },
      // Only 3 domains will be returned because the limit is set to 3 for gws eligibility check.
      // {
      //   prodInstName: 'SUGARCLOUDS9.NET',
      //   prodInstId: 'WN.D.*********',
      //   accountId: *********,
      // },
      // {
      //   prodInstName: 'SUGARCLOUDS9.ORG',
      //   prodInstId: 'WN.D.*********',
      //   accountId: *********,
      // },
    ]);
  }, 25000);

  it('6. Handles service errors', async () => {
    jest.spyOn(CONFIGS.qa.BLUEHOST, 'getConfig').mockRejectedValue(new Error('Service Error'));

    const apiRes = await request(app).post('/api/v1/getXSell').send(defaultPayload);

    expect(apiRes.body).toEqual(
      expect.objectContaining({
        error: true,
        message: 'Service Error',
      }),
    );
  });
});
