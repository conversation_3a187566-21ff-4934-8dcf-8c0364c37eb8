import request from 'supertest';
import app from '../../index';
import defaultPayloadJson from './data/default-payload.json';
import { CONFIGS } from 'app/cache/configCache';
import bluehostRules from './data/amwi-qa-bluehost-rules.json';
import { sfLogger } from 'app/logger';

const defaultPayload = JSON.parse(JSON.stringify(defaultPayloadJson));

beforeAll(() => {
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
  jest.spyOn(CONFIGS.qa.BLUEHOST, 'getConfig').mockResolvedValue(bluehostRules as any);
  sfLogger.error = jest.fn();
  sfLogger.info = jest.fn();
});

describe('xSellServiceAMWI', () => {
  
  it('1. Should return correct ads', async () => {
    defaultPayload.containerName = "AMWI"; 

    const xSellResponse = await request(app).post('/api/v1/getXSell').send(defaultPayload);
    const ads = JSON.parse(xSellResponse.text).response;

    expect(ads).toHaveLength(1);
    expect(ads[0].adDetails.adIdentifier).toBe('NO_ACTIVE_HOSTING_OR_ECOMM');

  });

  it('2. Handles service errors', async () => {
    jest.spyOn(CONFIGS.qa.BLUEHOST, 'getConfig').mockRejectedValue(new Error('Service Error'));

    const apiRes = await request(app).post('/api/v1/getXSell').send(defaultPayload);

    expect(apiRes.body).toEqual(
      expect.objectContaining({
        error: true,
        message: 'Service Error',
      }),
    );
  });
});
