{"rules": [{"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "redirect", "businessRule": "NO_ACTIVE_HOSTING_OR_ECOMM", "redirect": "https://www.bluehost.com/wordpress/wordpress-hosting?channelid=P61C100S1N0B3A151D134E0000V100#pricing-cards", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P61C100S1N0B3A151D134E0000V100", "channelID": "263", "coupon": "", "priority": 1, "fragmentDetails": {"adSku": "", "sku": "", "htmlPath": "/content/experience-fragments/bluehost/account_manager/home_cards/hosting_family/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/account_manager/home_cards/hosting_family/master.html"}, "fragmentJsonPath": [], "testId": 14}, {"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "redirect", "businessRule": "ANYONE_ELIGIBLE", "redirect": "https://www.bluehost.com/my-account/domain-center/domain-transfer?channelid=P13C100S1N0B3A151D134E0000V101", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P13C100S1N0B3A151D134E0000V101", "channelID": "263", "coupon": "", "priority": 2, "fragmentDetails": {"adSku": "", "htmlPath": "/content/experience-fragments/bluehost/account_manager/home_cards/domain_transfer/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/account_manager/home_cards/domain_transfer/master.html"}, "fragmentJsonPath": [], "testId": 15}, {"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P118C100S1N0B3A151D134E0000V100", "channelID": "263", "businessRule": "GT_1_BASIC_HOSTING_NO_CHOICEPLUS_OR_PRO_OR_WPSOLUTIONS_OR_ECOMM", "coupon": "", "priority": 3, "fragmentDetails": {"adSku": "WP_SOLUTION_CREATOR(WP_SOLUTION_YITH:1:WP_SOLUTION_YOAST:1)", "sku": "WP_SOLUTION_CREATOR", "htmlPath": "/content/experience-fragments/bluehost/account_manager/home_cards/wp_solution_creator/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/account_manager/home_cards/wp_solution_creator/master.html"}, "fragmentJsonPath": [], "testId": 16}, {"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "businessRule": "GT_1_CHOICEPLUS_OR_PRO_NO_BASIC_OR_WPSOLUTIONS_OR_ECOMM", "redirect": "", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P118C100S1N0B3A151D134E0000V102", "channelID": "263", "coupon": "", "priority": 4, "fragmentDetails": {"adSku": "WP_SOLUTION_COMMERCE(WP_SOLUTION_YITH:1:WP_SOLUTION_YOAST:1)", "sku": "WP_SOLUTION_COMMERCE", "htmlPath": "/content/experience-fragments/bluehost/account_manager/home_cards/wp_solution_commerce/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/account_manager/home_cards/wp_solution_commerce/master.html"}, "fragmentJsonPath": [], "testId": 17}, {"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "checkDomainsEligibleGWS": true, "businessRule": "ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL_NO_TITAN", "redirect": "", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P181C100S1N0B3A151D134E0000V102", "channelID": "263", "coupon": "", "priority": 5, "fragmentDetails": {"adSku": "GOOGLE_WORKSPACE_STARTER(GOOGLE_SEAT:1)", "sku": "GOOGLE_WORKSPACE_STARTER", "htmlPath": "/content/experience-fragments/bluehost/account_manager/home_cards/google_workspace/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/account_manager/home_cards/google_workspace/master.html"}, "fragmentJsonPath": [], "testId": 18}, {"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "businessRule": "ACTIVE_DOMAIN_EXCEEDS_ACTIVE_SSLCERT", "redirect": "", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P90C100S1N0B3A151D134E0000V100", "channelID": "263", "coupon": "", "priority": 6, "fragmentDetails": {"adSku": "SSL_DV", "sku": "SSL_DV", "htmlPath": "/content/experience-fragments/bluehost/account_manager/home_cards/ssl_family/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/account_manager/home_cards/ssl_family/master.html"}, "fragmentJsonPath": [], "testId": 19}, {"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "businessRule": "ACTIVE_DOMAIN_EXCEEDS_PRREG", "redirect": "", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P68C100S1N0B3A151D134E0000V100", "channelID": "263", "coupon": "", "priority": 7, "fragmentDetails": {"adSku": "PRI_REG_V2", "sku": "PRI_REG_V2", "htmlPath": "/content/experience-fragments/bluehost/account_manager/home_cards/domain_privacy/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/account_manager/home_cards/domain_privacy/master.html"}, "fragmentJsonPath": [], "testId": 20}, {"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "businessRule": "ACTIVE_HOSTING_EXCEEDS_YOAST_SEO_PREMIUM", "redirect": "", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P181C100S1N0B3A151D134E0000V100", "channelID": "263", "coupon": "", "priority": 8, "fragmentDetails": {"adSku": "YOAST_SEO_PREMIUM", "sku": "YOAST_SEO_PREMIUM", "htmlPath": "/content/experience-fragments/bluehost/account_manager/home_cards/yoast/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/account_manager/home_cards/yoast/master.html"}, "fragmentJsonPath": [], "testId": 21}, {"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "redirect", "businessRule": "ACTIVE_HOSTING_OR_ECOMM_NO_PURCH_LAST_7", "redirect": "https://www.bluehost.com/wordpress/wordpress-hosting?channelid=P61C100S1N0B3A151D134E0000V101#pricing-cards", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P61C100S1N0B3A151D134E0000V101", "channelID": "263", "coupon": "", "priority": 9, "fragmentDetails": {"adSku": "", "htmlPath": "content/experience-fragments/bluehost/account_manager/home_cards/hosting_family2/master.html", "htmlPathQA": "content/experience-fragments/bluehost/account_manager/home_cards/hosting_family2/master.html"}, "fragmentJsonPath": [], "testId": 22}, {"brand": "BLUEHOST", "url": "/home", "containerName": "AMHPCards", "actionType": "redirect", "businessRule": "KBSEARCH", "redirect": "https://www.bluehost.com/help", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P181C100S1N0B3A151D134E0000V104", "channelID": "263", "coupon": "", "priority": 10, "fragmentDetails": {"adSku": "", "htmlPath": "/content/experience-fragments/bluehost/account_manager/home_cards/search_kb/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/account_manager/home_cards/search_kb/master.html"}, "fragmentJsonPath": [], "testId": 23}]}