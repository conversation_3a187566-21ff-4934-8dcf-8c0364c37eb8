{"rules": [{"brand": "BLUEHOST", "ruleNumInConfluence": 2, "url": "/checkout", "containerName": "InCart", "actionType": "addToCart", "cartIncludesAll": [], "cartIncludesAny": ["BH_PKG_WP_ENT", "BH_HP_PKG_STARTER", "BH_PKG_WP_PLUS", "BH_HP_PKG_PLUS"], "cartShouldNotInclude": ["WP_SOLUTION_CREATOR", "WP_SOLUTION_SERVICE", "WP_SOLUTION_COMMERCE", "BH_ECOMM_STORE", "BH_ECOMM_STORE_MKTPL"], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P118C100S1N0B200A151D277E0000V100", "channelID": "263", "priority": 1, "fragmentDetails": {"adSku": "WP_SOLUTION_CREATOR(WP_SOLUTION_YITH:1:WP_SOLUTION_YOAST:1)", "htmlPath": "/content/experience-fragments/bluehost/upp/wordpress_solutionscreator/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/upp/wordpress_solutionscreator/master.html"}, "fragmentJsonPath": [], "testId": 7}, {"brand": "BLUEHOST", "ruleNumInConfluence": 3, "url": "/checkout", "containerName": "InCart", "actionType": "addToCart", "cartIncludesAll": [], "cartIncludesAny": ["BH_PKG_WP_PRO", "BH_PKG_WP_CHOICE_PLUS"], "cartShouldNotInclude": ["WP_SOLUTION_CREATOR", "WP_SOLUTION_SERVICE", "WP_SOLUTION_COMMERCE", "BH_ECOMM_STORE", "BH_ECOMM_STORE_MKTPL"], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P118C100S1N0B200A151D277E0000V102", "channelID": "263", "priority": 1, "fragmentDetails": {"adSku": "WP_SOLUTION_COMMERCE(WP_SOLUTION_YITH:1:WP_SOLUTION_YOAST:1)", "htmlPath": "/content/experience-fragments/bluehost/upp/wordpress_commercesolution/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/upp/wordpress_commercesolution/master.html"}, "fragmentJsonPath": [], "testId": 8}, {"brand": "BLUEHOST", "ruleNumInConfluence": 1, "url": "/checkout", "containerName": "InCart", "actionType": "addToCart", "minDomainsInCart": 1, "cartIncludesAll": [], "cartIncludesAny": [], "cartShouldNotInclude": ["BH_PKG_WP_ENT", "BH_PKG_WP_PLUS", "BH_PKG_WP_CHOICE_PLUS", "BH_PKG_WP_PRO", "BH_ECOMM_STORE", "BH_ECOMM_STORE_MKTPL", "WP_CLOUD_1", "WP_CLOUD_10", "WP_CLOUD_25", "WP_CLOUD_50", "BH_VPS_NVME_4_CPANEL", "BH_VPS_NVME_8_CPANEL", "BH_VPS_NVME_16_CPANEL", "BH_DEDI_NVME_32_CPANEL", "BH_DEDI_NVME_64_CPANEL", "BH_DEDI_NVME_128_CPANEL"], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P195C100S1N0B200A151D277E0000V100", "channelID": "263", "priority": 1, "fragmentDetails": {"adSku": "BH_PKG_WP_ENT", "htmlPath": "/content/experience-fragments/bluehost/upp/wordpress_basic/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/upp/wordpress_basic/master.html"}, "fragmentJsonPath": [], "testId": 9}, {"brand": "BLUEHOST", "ruleNumInConfluence": 6, "url": "/checkout", "containerName": "InCart", "checkDomainsEligibleGWS": true, "actionType": "addToCart", "minDomainsInCart": 1, "cartIncludesAll": [], "cartIncludesAny": [], "cartShouldNotInclude": ["E_PRO", "E_PRO_PLUS", "GOOGLE_WORKSPACE_STARTER", "GOOGLE_WORKSPACE_STANDARD", "GOOGLE_WORKSPACE_PLUS", "TITAN_MAIL_PRO", "TITAN_MAIL_STD", "TITAN_MAIL_TRIAL", "TITAN_MAIL_SEAT"], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P181C100S1N0B200A151D277E0000V101", "channelID": "263", "coupon": "50OFFGWBH", "priority": 6, "fragmentDetails": {"adSku": "GOOGLE_WORKSPACE_STARTER(GOOGLE_SEAT:1)", "sku": "GOOGLE_WORKSPACE_STARTER", "htmlPath": "/content/experience-fragments/bluehost/upp/google_workspace/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/upp/google_workspace/master.html"}, "fragmentJsonPath": [], "testId": 10}, {"brand": "BLUEHOST", "ruleNumInConfluence": 7, "url": "/checkout", "containerName": "InCart", "actionType": "addToCart", "cartIncludesAll": [], "cartIncludesAny": ["BH_PKG_WP_ENT", "BH_PKG_WP_PLUS"], "cartShouldNotInclude": ["CODEGUARD_BASIC_V2"], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P180C100S1N0B200A151D277E0000V100", "channelID": "263", "priority": 7, "fragmentDetails": {"adSku": "CODEGUARD_BASIC_V2", "htmlPath": "/content/experience-fragments/bluehost/upp/codeguard/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/upp/codeguard/master.html"}, "fragmentJsonPath": [], "testId": 11}, {"brand": "BLUEHOST", "ruleNumInConfluence": 8, "url": "/checkout", "containerName": "InCart", "actionType": "addToCart", "cartIncludesAll": [], "cartIncludesAny": ["BH_PKG_WP_ENT", "BH_PKG_WP_PLUS", "BH_PKG_WP_CHOICE_PLUS", "BH_ECOMM_STORE", "BH_ECOMM_STORE_MKTPL"], "cartShouldNotInclude": ["SITELOCK_ESSENTIALS"], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P171C100S1N0B200A151D277E0000V100", "channelID": "263", "priority": 8, "fragmentDetails": {"adSku": "SITELOCK_ESSENTIALS", "htmlPath": "/content/experience-fragments/bluehost/upp/sitelock/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/upp/sitelock/master.html"}, "fragmentJsonPath": [], "testId": 12}, {"brand": "BLUEHOST", "ruleNumInConfluence": 9, "url": "/checkout", "containerName": "InCart", "actionType": "addToCart", "cartIncludesAll": [], "cartIncludesAny": ["WP_CLOUD_1", "WP_CLOUD_10", "WP_CLOUD_25", "WP_CLOUD_50", "BH_VPS_NVME_4_CPANEL", "BH_VPS_NVME_8_CPANEL", "BH_VPS_NVME_16_CPANEL", "BH_DEDI_NVME_32_CPANEL", "BH_DEDI_NVME_64_CPANEL", "BH_DEDI_NVME_128_CPANEL"], "cartShouldNotInclude": ["PRO_DESIGN_LIVE_BASIC"], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P181C100S1N0B200A151D277E0000V100", "channelID": "263", "priority": 9, "fragmentDetails": {"adSku": "PRO_DESIGN_LIVE_BASIC", "htmlPath": "/content/experience-fragments/bluehost/upp/pro_design_live_basic/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/upp/pro_design_live_basic/master.html"}, "fragmentJsonPath": [], "testId": 13}, {"brand": "BLUEHOST", "ruleNumInConfluence": 999, "url": "/checkout", "containerName": "InCart", "actionType": "addToCart", "cartIncludesAll": ["TESTING_SKU_0", "TESTING_SKU_1"], "cartIncludesAny": [], "cartShouldNotInclude": [], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P171C100S1N0B200A151D277E0000V100", "channelID": "263", "priority": 8, "fragmentDetails": {"adSku": "TESTING_SKU_3", "htmlPath": "/content/experience-fragments/bluehost/upp/sitelock/master.html", "htmlPathQA": "/content/experience-fragments/bluehost/upp/sitelock/master.html"}, "fragmentJsonPath": [], "testId": 24}]}