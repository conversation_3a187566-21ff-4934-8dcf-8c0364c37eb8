{"rules": [{"brand": "NETWORKSOLUTIONS", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P68C100S1N0B200A151D134E0000V100", "channelID": "2", "businessRule": "ACTIVE_DOMAIN_EXCEEDS_PRREG", "coupon": "", "priority": 1, "fragmentDetails": {"adSku": "PRI_REG_V2", "htmlPath": "/content/experience-fragments/hostgator/promotion/codeguard/codeguard_product/master.html", "htmlPathQA": "/content/experience-fragments/hostgator/promotion/codeguard/codeguard_product/master.html"}, "fragmentJsonPath": []}, {"brand": "NETWORKSOLUTIONS", "url": "/home", "containerName": "AMHPCards", "actionType": "redirect", "businessRule": "NO_ACTIVE_HOSTING_OR_ECOMM", "redirect": "https://www.bluehost.com/wordpress/wordpress-hosting?channelid=P61C100S1N0B200A151D134E0000V101#pricing-cards", "productsInCart": [], "cartIncludesAll": [], "cartIncludesAny": [], "cartShouldNotInclude": [], "minDomainsInCart": 0, "domainsInCartExceedIncludedSku": [], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P61C100S1N0B200A151D134E0000V101", "channelID": "2", "coupon": "", "priority": 1, "packageEligible": false, "fragmentDetails": {"packageName": "", "adSku": "", "htmlPath": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html", "htmlPathQA": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html"}, "fragmentJsonPath": []}, {"brand": "NETWORKSOLUTIONS", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "businessRule": "CUST_HAS_ACTIVE_SHARED_HOSTING_WITHOUT_OTHERS", "productsInCart": [], "cartIncludesAll": [], "cartIncludesAny": [], "cartShouldNotInclude": [], "minDomainsInCart": 0, "domainsInCartExceedIncludedSku": [], "redirect": "", "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P118C100S1N0B200A151D134E0000V101", "channelID": "2", "coupon": "", "priority": 1, "packageEligible": false, "fragmentDetails": {"packageName": "", "adSku": "", "htmlPath": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html", "htmlPathQA": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html"}, "fragmentJsonPath": []}, {"brand": "NETWORKSOLUTIONS", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "checkDomainsEligibleGWS": true, "businessRule": "ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL", "redirect": "", "productsInCart": [], "cartIncludesAll": [], "cartIncludesAny": [], "cartShouldNotInclude": [], "minDomainsInCart": 0, "domainsInCartExceedIncludedSku": [], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P181C100S1N0B200A151D134E0000V105", "channelID": "2", "coupon": "", "priority": 1, "packageEligible": false, "fragmentDetails": {"packageName": "", "adSku": "GOOGLE_WORKSPACE_STARTER(GOOGLE_SEAT:1)", "htmlPath": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html", "htmlPathQA": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html"}, "fragmentJsonPath": []}, {"brand": "NETWORKSOLUTIONS", "url": "/home", "containerName": "AMHPCards", "actionType": "redirect", "businessRule": "ACTIVE_HOSTING_OR_ECOMM_NO_PURCH_LAST_7", "redirect": "https://www.bluehost.com/wordpress/wordpress-hosting?channelid=P61C100S1N0B200A151D134E0000V102#pricing-cards", "productsInCart": [], "cartIncludesAll": [], "cartIncludesAny": [], "cartShouldNotInclude": [], "minDomainsInCart": 0, "domainsInCartExceedIncludedSku": [], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P61C100S1N0B200A151D134E0000V102", "channelID": "2", "coupon": "", "priority": 1, "packageEligible": false, "fragmentDetails": {"packageName": "", "adSku": "", "htmlPath": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html", "htmlPathQA": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html"}, "fragmentJsonPath": []}, {"brand": "NETWORKSOLUTIONS", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "businessRule": "ACTIVE_HOSTING_EXCEEDS_PAID_SITELOCK", "redirect": "", "productsInCart": [], "cartIncludesAll": [], "cartIncludesAny": [], "cartShouldNotInclude": [], "minDomainsInCart": 0, "domainsInCartExceedIncludedSku": [], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P171C100S1N0B200A151D134E0000V101", "channelID": "2", "coupon": "", "priority": 1, "packageEligible": false, "fragmentDetails": {"packageName": "", "adSku": "", "htmlPath": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html", "htmlPathQA": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html"}, "fragmentJsonPath": []}, {"brand": "NETWORKSOLUTIONS", "url": "/home", "containerName": "AMHPCards", "actionType": "openCTB", "businessRule": "ACTIVE_HOSTING_EXCEEDS_YOAST_SEO_PREMIUM", "redirect": "", "productsInCart": [], "cartIncludesAll": [], "cartIncludesAny": [], "cartShouldNotInclude": [], "minDomainsInCart": 0, "domainsInCartExceedIncludedSku": [], "pricingTerm": "YEAR", "priceDisplayTerm": "month", "landingCode": "P167C100S1N0B200A151D134E0000V101", "channelID": "2", "coupon": "", "priority": 1, "packageEligible": false, "fragmentDetails": {"packageName": "", "adSku": "", "htmlPath": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html", "htmlPathQA": "/content/experience-fragments/webdotcom/upp/diy_website_builder/master.html"}, "fragmentJsonPath": []}]}