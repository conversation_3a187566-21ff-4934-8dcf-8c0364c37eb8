import request from 'supertest';
import app from '../../index';
import defaultPayloadJson from './data/default-payload.json';
import { CONFIGS } from 'app/cache/configCache';
import bluehostRules from './data/amhp-qa-bluehost-rules.json';
import { sfLogger } from 'app/logger';

const defaultPayload = JSON.parse(JSON.stringify(defaultPayloadJson));

beforeAll(() => {
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
  jest.spyOn(CONFIGS.qa.BLUEHOST, 'getConfig').mockResolvedValue(bluehostRules as any);
  sfLogger.error = jest.fn();
  sfLogger.info = jest.fn();
});

describe('xSellServiceAMHP', () => {
  it('1. Should return correct ads', async () => {
    defaultPayload.containerName = 'AMHPCards';
    defaultPayload.accountId = *********;
    defaultPayload.userId = *********;
    defaultPayload.isLoggedIn = true;

    const xSellResponse = await request(app).post('/api/v1/getXSell').send(defaultPayload);
    const ads = JSON.parse(xSellResponse.text).response;
    const priorities = ads.map((ad: any) => ad.priority);
    const sortedPriorities = priorities.sort((a: number, b: number) => a - b);
    const adIdentifiers = ads.map((ad: any) => ad.adDetails.adIdentifier);

    expect(ads).toHaveLength(6);
    expect(priorities).toEqual(sortedPriorities);
    expect(adIdentifiers).toContain('NO_ACTIVE_HOSTING_OR_ECOMM');
    expect(adIdentifiers).toContain('ANYONE_ELIGIBLE');
    expect(adIdentifiers).toContain('ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL_NO_TITAN'); // <-- GOOGLE WORKSPACES
    expect(adIdentifiers).toContain('ACTIVE_DOMAIN_EXCEEDS_ACTIVE_SSLCERT');
    expect(adIdentifiers).toContain('ACTIVE_DOMAIN_EXCEEDS_PRREG');
    expect(adIdentifiers).toContain('KBSEARCH');

    // expect(ads[0].adDetails.adIdentifier).toBe('NO_ACTIVE_HOSTING_OR_ECOMM');
    // expect(ads[1].adDetails.adIdentifier).toBe('ANYONE_ELIGIBLE');
    // expect(ads[2].adDetails.adIdentifier).toBe('ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL_NO_TITAN'); // <-- GOOGLE WORKSPACES
    // expect(ads[3].adDetails.adIdentifier).toBe('ACTIVE_DOMAIN_EXCEEDS_ACTIVE_SSLCERT');
    // expect(ads[4].adDetails.adIdentifier).toBe('ACTIVE_DOMAIN_EXCEEDS_PRREG');
    // expect(ads[5].adDetails.adIdentifier).toBe('KBSEARCH');
  });

  it('2. Handles service errors', async () => {
    jest.spyOn(CONFIGS.qa.BLUEHOST, 'getConfig').mockRejectedValue(new Error('Service Error'));

    const apiRes = await request(app).post('/api/v1/getXSell').send(defaultPayload);

    expect(apiRes.body).toEqual(
      expect.objectContaining({
        error: true,
        message: 'Service Error',
      }),
    );
  });
});
