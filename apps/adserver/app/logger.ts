import axios from "axios";
import * as crypto from 'node:crypto';
import { Env } from "./models/aemRules";

export interface LogDataModel {
    message: string;
    eventType: string;
    errorCode?: string;
    errorDetails?: any;
    siteId?: string;
    userId?: string;
    customAttributes?: any;
}

export enum SF_LOG_LEVEL {
    TRACE = 1,
    VERBOSE = 2,
    INFO = 3,
    DEBUG = 4,
    WARN = 5,
    ERROR = 6,
}

export function getRngString(length = 64) {
    return crypto.randomBytes(length).toString("hex");
}

const levels = ['error', 'warn', 'info', 'verbose', 'debug', 'trace'];

async function logEvent(logData: any) {
    const logDataModel = {
        level: '',
        applicationId: '',
        eventType: '',
        brand: '',
        message: '',
        errorCode: '',
        errorDetails: '',
        siteId: '',
        userId: '',
        environment: '',
        customAttributes: '',
        host: '',
    };

    const filteredLogData = Object.keys(logDataModel).reduce((acc: any, key) => {
        if (logData[key]) {
            acc[key] = logData[key];
        }

        return acc;
    }, {});

    const url = 'https://sfbff.newfold.com/logEvents/sendEvent';

    // const headers = {
    //     'Content-Type': 'application/json',
    // };

    //Required data: level and applicationId
    //Check if level and applicationID are included
    if (levels.includes(filteredLogData.level) && filteredLogData.applicationId) {
        try {
            // const response = await fetch(url, {
            //     method: 'POST',
            //     headers,
            //     body: JSON.stringify(filteredLogData),
            // });

            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url,
                headers: {
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify(filteredLogData)
            };

            const response = await axios.request(config);

            if (response.status >= 300) {
                throw new Error('Failed to log event');
            }
        } catch (error) {
            console.error('Error logging event:', error);
        }
    } else {
        console.error(`Invalid log level ${filteredLogData.level} or missing applicationId`);
    }
}

class SFLogger {
    build: Env = "qa";
    applicationId = "";

    /**
     * Helper function to handle a variety of scenarios
     * @param level 
     * @returns {HANDLE_LOG_RESULT}
     */
    handleLogRequest(level: any) {
        let result = "CONSOLE_LOG";

        if(this.build === 'prod') {
            result = 'SEND_LOG'
        }
        // const env = this.build.toUpperCase();

        // const levelFromConfig = this.loggerConfig[brand][env];
        // const sfLogLevelEnum = SF_LOG_LEVEL[levelFromConfig];

        // const x = SF_LOG_LEVEL[this.build]

        // if (level >= sfLogLevelEnum) {
        //     result = 'SEND_LOG';
        // }

        return result;
        // return 'CONSOLE_LOG';
    }

    info<LogData extends LogDataModel>(logData: LogData, rngTrace: string) {
        this.log(logData, SF_LOG_LEVEL.INFO, rngTrace);
    }

    warn<LogData extends LogDataModel>(logData: LogData, rngTrace: string) {
        this.log(logData, SF_LOG_LEVEL.WARN, rngTrace);
    }

    error<LogData extends LogDataModel>(logData: LogData, rngTrace: string) {
        this.log(logData, SF_LOG_LEVEL.ERROR, rngTrace);
    }

    verbose<LogData extends LogDataModel>(logData: LogData, rngTrace: string) {
        this.log(logData, SF_LOG_LEVEL.VERBOSE, rngTrace);
    }

    debug<LogData extends LogDataModel>(logData: LogData, rngTrace: string) {
        this.log(logData, SF_LOG_LEVEL.DEBUG, rngTrace);
    }

    trace<LogData extends LogDataModel>(logData: LogData, rngTrace: string) {
        this.log(logData, SF_LOG_LEVEL.TRACE, rngTrace);
    }

    /**
     * The first is an object that has a lower bound of {@link LogDataModel}. So you can pass anything you want to log.
     * The second argument is your log level of type {@link SF_LOG_LEVEL}
     * The third argument is the trace, which we can use to group together related logs
     * 
     * Check the types and definitions for more details
     * 
     * @param {T} logData 
     * @param {SF_LOG_LEVEL} level 
     */
    private log(logData: LogDataModel, level: SF_LOG_LEVEL, rngTrace: string) {
        try {
            if (!logData.customAttributes) {
                logData.customAttributes = {}
            }
            else {
                if(rngTrace)
                    logData.customAttributes.rngTrace = rngTrace;
            }

            switch (this.handleLogRequest(level)) {
                case 'CONSOLE_LOG':
                    if (level === SF_LOG_LEVEL.TRACE || level === SF_LOG_LEVEL.VERBOSE) {
                        console.info(`log level ${level} not supported in browser console, defaulting to log`);
                        level = SF_LOG_LEVEL.DEBUG;
                    }
                    // console[this.getLevelName(level).toLowerCase()](logData);
                    break;

                case 'SEND_LOG':
                    logEvent({
                        ...logData,
                        environment: this.build,
                        level: this.getLevelName(level).toLowerCase(),
                        applicationId: this.applicationId
                    })
                    break;
                case 'OTHER':
                    // Keeping an option for future scope. Or you can just append to the type
                    break;
                case 'DO_NOTHING':
                default:
                    break;
            }
        }
        catch (e) {
            console.error("something went wrong while logging");
            console.error(e);
        }
    }

    /**
     * @constructor
     */
    constructor(build: Env, appId: string) {
        this.build = build;
        this.applicationId = appId;
    }

    /**
     * Returns the string representation of the enum
     * @param {string} level 
     * @returns 
     */
    getLevelName(level: SF_LOG_LEVEL) {
        switch (level) {
            case SF_LOG_LEVEL.DEBUG:
                return "DEBUG";
            case SF_LOG_LEVEL.ERROR:
                return "ERROR";
            case SF_LOG_LEVEL.INFO:
                return "INFO";
            case SF_LOG_LEVEL.TRACE:
                return "TRACE";
            case SF_LOG_LEVEL.VERBOSE:
                return "VERBOSE";
            case SF_LOG_LEVEL.WARN:
                return "WARN";
        }
    }
};

export const sfLogger = new SFLogger(process.env.NODE_ENV === 'production' ? 'prod' : 'qa', 'ADAM');
