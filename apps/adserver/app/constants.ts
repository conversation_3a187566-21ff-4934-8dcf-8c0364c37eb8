export const WHITELIST = [
  'http://localhost',
  'http://127.0.0.1',
  `https://www.bluehost.com`,
  `https://www.bluehost.in`,
  `https://www.domain.com`,
  `https://cms65-qa.domain.com`,
  `https://www.hostgator.com`,
  `https://www.dotster.com`,
  `https://www.mydomain.com`,
  `https://www.netfirms.com`,
  `https://www.web.com`,
  `https://www.register.com`,
  `https://www.networksolutions.com`,
];

export const getSwaggerHtml = (surface: string) => {
  return `
  <html>
	  <head>    
		  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@3.17.0/swagger-ui.css">
		  <script src="//unpkg.com/swagger-ui-dist@3/swagger-ui-bundle.js"></script>
		  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.17.14/swagger-ui.css" integrity="sha512-MvYROlKG3cDBPskMQgPmkNgZh85LIf68y7SZ34TIppaIHQz1M/3S/yYqzIfufdKDJjzB9Qu1BV63SZjimJkPvw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
		  <script>
  
			  function render() {
				  var ui = SwaggerUIBundle({
					  url:  '/apidocs/swaggerJson/${surface}',
					  dom_id: '#swagger-ui',
					  presets: [
						  SwaggerUIBundle.presets.apis,
						  SwaggerUIBundle.SwaggerUIStandalonePreset
					  ],
						onComplete: () => {
							const schemeContainer = document.querySelector('.swagger-ui .scheme-container');
							if (schemeContainer) {
								const surfaceSection = document.createElement('section');
								surfaceSection.id = 'surface-section';
								surfaceSection.className = 'schemes wrapper block col-12';
								surfaceSection.style.marginTop = '1rem';

								surfaceSection.innerHTML = \`
									<div>
										<span class="servers-title">Surfaces</span>
										<div class="surfaces">
											<label for="surfaces">
												<select id="surface-select">
													<option value="cart" ${surface === 'cart' ? 'selected' : ''}>Cart</option>
													<option value="amwi" ${surface === 'amwi' ? 'selected' : ''}>AM Welcome Interstitial</option>
													<option value="amhp" ${surface === 'amhp' ? 'selected' : ''}>AM Home Page</option>
													<option value="expFrag" ${surface === 'expFrag' ? 'selected' : ''}>Experience Fragment</option>

												</select>
											</label>
										</div>
									</div>
								\`;

								schemeContainer.appendChild(surfaceSection);

								const selectElement = document.getElementById('surface-select');
								const baseUrl = window.location.origin;

								if (selectElement) {
									selectElement.addEventListener('change', (event) => {
										const selectedValue = event.target.value;
										if (selectedValue) {											
											window.location.href = baseUrl + '/apidocs/' + selectedValue;
										}
									});
								}
							}
						}
				  });
			  }
  
		  </script>
	  </head>
  
	  <body onload="render()">
		  <div id="swagger-ui"></div>
	  </body>
  </html>
  `;
};

export const BRANDS = ['BLUEHOST', 'HOSTGATOR', 'NETWORKSOLUTIONS'];

export const RESPONSE_TYPES = ['fragId', 'frag', 'rawFrag'];

export const AMHPCards = 'AMHPCards';
export const InCart = 'InCart';
export const AMWI = 'AMWI';

export const CONTAINER_NAME_TYPES = [AMHPCards, InCart, AMWI];

export const CHANNEL_TYPES = ['Web', 'Mobile', 'Tablet'];

// allow us to specify whether to show the ad based on the logged in status
export const LOGGED_IN_STATUS_ALLOWED_LOGGED_IN = 'loggedIn';
export const LOGGED_IN_STATUS_ALLOWED_NOT_LOGGED_IN = 'notLoggedIn';
export const LOGGED_IN_STATUS_ALLOWED_ANY = 'any';

export enum EVENT_TYPES {
  AemRules = 'ADAM_xSellServiceUppCart_AEM_RULES',
  DbRulesNotFound = 'ADAM_DB_RULES',
  RuleEvaluation = 'ADAM_xSellServiceUppCart_RULE_EVALUATION',
  AmwiRuleEvaluation = 'ADAM_xSellServiceAMWI_RULE_EVALUATION',
  Success = 'ADAM_xSellService_SUCCESS',
  Success_UPP = 'ADAM_xSellServiceUppCart_SUCCESS',
  Success_AMHP = 'ADAM_xSellServiceAMHPCards_SUCCESS',
  Success_AMWI = 'ADAM_xSellServiceAMWI_SUCCESS',
  Error = 'ADAM_xSellServiceUppCart_CE_3',
  ExpFragError = 'ADAM_getExpFrag_CE_1',
  Start = 'ADAM_xSellController_Start',
  BeforeTruncate = 'ADAM_xSellController_Before_Truncate',
  DataFiltered = 'ADAM_xSellController_Data_Filtered',
  Validation_Fail = 'ADAM_xSellController_Validation_Fail',
  ContainerName_CE_1 = 'ADAM_xSellController_ContainerName_CE_1',
  xSellController_Response = 'ADAM_xSellController_Response',
  Response_AMHomePageCards = 'ADAM_xSellController_Response_AMHomePageCards',
  Response_InCart = 'ADAM_xSellController_Response_InCart',
  Response_AMWI = 'ADAM_xSellController_Response_AMWI',
  GwsEligibilityCheck = 'ADAM_GwsEligibilityCheck',
  xSellController = 'ADAM_xSellController',
  PriceInjection = 'ADAM_PriceInjection',

  xSellServiceNewCart_CE_1 = 'ADAM_xSellServiceNewCart_CE_1',
  xSellServiceNewCart_CE_2 = 'ADAM_xSellServiceNewCart_CE_2',

  xSellServiceUppCart_CE_2 = 'ADAM_xSellServiceUppCart_CE_2',
  xSellServiceUppCart_CE_3 = 'ADAM_xSellServiceUppCart_CE_3',
  xSellServiceUppCart_CE_4 = 'ADAM_xSellServiceUppCart_CE_4',
  CORS = 'ADAM_Cors',
  QueryAccounts_Fail = 'ADAM_QUERY_ACCTS_FOR_ACTIVE_PRODUCTS',
  QueryUser_Fail = 'ADAM_QUERY_USER_DETAILS',
  xSellServiceAMWi_QueryAccounts_Fail = 'ADAM_xSellServiceAMWi_QUERY_ACCTS',
  xSellServiceAMWi_QueryProducts_Fail = 'ADAM_xSellServiceAMWi_QUERY_PRODUCTS',
  xSellServiceAMTestOffers = 'ADAM_xSellServiceAMTestOffers_CE_1',
  xSellServiceAMWi_CE_1 = 'ADAM_xSellServiceAMWi_CE_1',
  xSellServiceAMWi_CE_2 = 'ADAM_xSellServiceAMWi_CE_2',
  xSellServiceAMWi_CE_3 = 'ADAM_xSellServiceAMWi_CE_3',
  xSellServiceAMHP_QueryAccounts_Fail = 'ADAM_xSellServiceAMHP_QUERY_ACCTS',
  xSellServiceAMHP_QueryProducts_Fail = 'ADAM_xSellServiceAMHP_QUERY_PRODUCTS',
  xSellServiceAMHP_CE_1 = 'ADAM_xSellServiceAMHP_CE_1',
  xSellServiceAMHP_CE_2 = 'ADAM_xSellServiceAMHP_CE_2',
  xSellServiceAMHP_CE_3 = 'ADAM_xSellServiceAMHP_CE_3',
  xSellServiceAMWi_CE_4 = 'ADAM_xSellServiceAMWi_CE_4',
  ADAM_getProductPrice = 'ADAM_getProductPrice',
  ADAM_BusinessRulesHelper = 'ADAM_BusinessRulesHelper',
  CheckForEligibleSites = 'ADAM_CheckForEligibleSites',
  PushDecisionsToDB_Fail = 'ADAM_PushDecisionsToDB_Fail',
}

// group together each with any alias
export enum AM_BUSINESS_RULES {
  ACTIVE_DOMAIN_EXCEEDS_PRREG = 'ACTIVE_DOMAIN_EXCEEDS_PRREG',
  WI_PRREG = 'WI_PRREG',
  HOME_PRREG = 'HOME_PRREG',

  NO_ACTIVE_HOSTING_OR_ECOMM = 'NO_ACTIVE_HOSTING_OR_ECOMM',
  WI_HOSTING_FAMILY_NO_ACTIVE = 'WI_HOSTING_FAMILY_NO_ACTIVE',
  HOME_HOSTING_FAMILY_NO_ACTIVE = 'HOME_HOSTING_FAMILY_NO_ACTIVE',

  CUST_HAS_ACTIVE_SHARED_HOSTING_WITHOUT_OTHERS = 'CUST_HAS_ACTIVE_SHARED_HOSTING_WITHOUT_OTHERS',
  CUST_HAS_ACTIVE_SHARED_HOSTING_AND_CLOUD_HOSTING_WITHOUT_OTHERS = 'CUST_HAS_ACTIVE_SHARED_HOSTING_AND_CLOUD_HOSTING_WITHOUT_OTHERS',
  WI_WP_SOLUTION_FAMILY = 'WI_WP_SOLUTION_FAMILY',
  HOME_WP_SOLUTION_FAMILY = 'HOME_WP_SOLUTION_FAMILY',

  ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL_NO_TITAN = 'ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL_NO_TITAN',
  WI_GOOGLE_WORKSPACE_NO_TITAN = 'WI_GOOGLE_WORKSPACE_NO_TITAN',
  HOME_GOOGLE_WORKSPACE_NO_TITAN = 'HOME_GOOGLE_WORKSPACE_NO_TITAN',

  ACTIVE_HOSTING_OR_ECOMM_NO_PURCH_LAST_7 = 'ACTIVE_HOSTING_OR_ECOMM_NO_PURCH_LAST_7',
  WI_HOSTING_FAMILY_HAS_ACTIVE = 'WI_HOSTING_FAMILY_HAS_ACTIVE',
  HOME_HOSTING_FAMILY_HAS_ACTIVE = 'HOME_HOSTING_FAMILY_HAS_ACTIVE',

  ACTIVE_HOSTING_EXCEEDS_PAID_SITELOCK = 'ACTIVE_HOSTING_EXCEEDS_PAID_SITELOCK',
  WI_SITELOCK = 'WI_SITELOCK',
  HOME_SITELOCK = 'HOME_SITELOCK',

  WI_PRO_EMAIL = 'WI_PRO_EMAIL',
  HOME_PRO_EMAIL = 'HOME_PRO_EMAIL',

  ACTIVE_HOSTING_EXCEEDS_YOAST_SEO_PREMIUM = 'ACTIVE_HOSTING_EXCEEDS_YOAST_SEO_PREMIUM',
  WI_YOAST_SEO_PREMIUM = 'WI_YOAST_SEO_PREMIUM',
  HOME_YOAST_SEO_PREMIUM = 'HOME_YOAST_SEO_PREMIUM',

  ACTIVE_DOMAIN_EXCEEDS_ACTIVE_SSLCERT = 'ACTIVE_DOMAIN_EXCEEDS_ACTIVE_SSLCERT',
  WI_SSL_FAMILY = 'WI_SSL_FAMILY',
  HOME_SSL_FAMILY = 'HOME_SSL_FAMILY',

  NO_ECOMDASH_OR_BUSESSENTIAL_OR_WEBMKTG_OR_ONLINE_STORE_MKT = 'NO_ECOMDASH_OR_BUSESSENTIAL_OR_WEBMKTG_OR_ONLINE_STORE_MKT',
  WI_ECOMMDASH_MYSCHEDULER = 'WI_ECOMMDASH_MYSCHEDULER',
  HOME_ECOMMDASH_MYSCHEDULER = 'HOME_ECOMMDASH_MYSCHEDULER',

  ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL = 'ACTIVE_DOMAIN_EXCEEDS_ACTIVE_EMAIL',
  WI_GOOGLE_WORKSPACE = 'WI_GOOGLE_WORKSPACE',
  HOME_GOOGLE_WORKSPACE = 'HOME_GOOGLE_WORKSPACE',

  ANYONE_ELIGIBLE = 'ANYONE_ELIGIBLE',
  HOME_DOMAIN_TRANSFER = 'HOME_DOMAIN_TRANSFER',

  KBSEARCH = 'KBSEARCH',
  HOME_KBSEARCH = 'HOME_KBSEARCH',

  HOME_WEBSITE_GRADER = 'HOME_WEBSITE_GRADER',
  WI_WEBSITE_GRADER = 'WI_WEBSITE_GRADER',

  PRO_WEBSITE500OFF_AMCARD_TEXT_ONLY = 'PRO_WEBSITE500OFF_AMCARD_TEXT_ONLY',
  HOME_PRO_WEBSITE500OFF_AMCARD_TEXT_ONLY = 'HOME_PRO_WEBSITE500OFF_AMCARD_TEXT_ONLY',

  HOME_DOM_ALT = 'HOME_DOM_ALT',

  BASIC_OR_PLUS_HOSTING_NO_CHOICEPLUS_OR_PRO_OR_WPSOLUTIONS_OR_ECOMM = 'BASIC_OR_PLUS_HOSTING_NO_CHOICEPLUS_OR_PRO_OR_WPSOLUTIONS_OR_ECOMM',
  HOME_SOLUTION_CREATOR = 'HOME_SOLUTION_CREATOR',

  VARIOUS_HOSTING_NO_BASIC_OR_WPSOLUTIONS_OR_ECOMM = 'VARIOUS_HOSTING_NO_BASIC_OR_WPSOLUTIONS_OR_ECOMM',
  HOME_SOLUTION_COMMERCE = 'HOME_SOLUTION_COMMERCE',

  UPP_WP_BASIC_EXISTING_CUSTOMER = 'UPP_WP_BASIC_EXISTING_CUSTOMER',

  WI_PRO_WEBSITE = 'WI_PRO_WEBSITE',
  HOME_PRO_WEBSITE = 'HOME_PRO_WEBSITE',
}

export enum ERROR_MESSAGES {
  AemRulesLoadingFailed = 'AEM rules loading failed',
  XSellServiceNewCartError = 'Error in xSellServiceNewCart',
  XSellServiceError = 'Error in xSellServiceUppCart',
  GetExpFragError = 'Error in getExpFrag',
  ValidationFailed = 'Request validation failed',
  ContainerNotFound = '400: containerName was not passed in the request and is required',
  ControllerError = 'Error in xSellController',
  ConfigUpdateFailed = 'Failed to update config',
  ResourceNotFound = 'The requested resource could not be found',
  QueryAccountsFailed = 'Failed to query the customers accounts',
  QueryProductsFailed = 'Failed to query the customers products',
  LargeUserSkipRuleEval = 'Skipping product rule eval for large user',
  QueryActiveProductsError = 'Error in getCustomerDetails',
  XSellServiceAMTestOffersError = 'Error in xSellServiceAMTestOffers',
  XSellServiceAMWiError = 'Error in xSellServiceAMWi',
  XSellServiceAMHPError = 'Error in XSellServiceAMHP',
  envInvalid = '400: The env param (development | qa | stg | prod | jarvisqa1) was not passed in the request and is required',
  PushDecisionsToDBFailed = 'Failed to push the ads to the decision table in database',
}

export enum EVENT_MESSAGES {
  AemRulesConfig = 'AEM Rules from config',
  XSellServiceTestOffersProcessed = 'Successfully processed xSellServiceTestOffers',
  XSellServiceProcessed = 'Successfully processed',
  XSellServiceUppProcessed = 'Successfully processed xSellServiceUppCart',
  XSellServiceAMHPProcessed = 'Successfully processed xSellServiceAMHPCards',
  XSellServiceAMWIProcessed = 'Successfully processed xSellServiceAMWI',
  EnteredController = 'Entered xSellController',
  ServiceResponseReceived = 'Service call response received',
  DomainsForGwsEligibility = 'Sending domains for GWS eligibility',
  siteEligibilityCheckPerformed = 'Successfully performed check for eligible sites',
}

export const FREE = 'FREE';
export const PROD = 'prod';
export const STAGE = 'stg';
export const QA = 'qa';
export const JARVISQA1 = 'jarvisqa1';
export const DEVELOPMENT = 'development';

export enum BUSINESS_RULE_PRODUCT_KEYS {
  activeDomains = 'activeDomains',
  activePR = 'activePR',
  activeFreeTrialPR = 'activeFreeTrialPR',
  activeEmail = 'activeEmail',
  activeHosting = 'activeHosting',
  activePaidSitelock = 'activePaidSitelock',
  activeSSL = 'activeSSL',
  activeSharedHosting = 'activeSharedHosting',
  activeSolutionHosting = 'activeSolutionHosting',
  activeWordpressSolutions = 'activeWordpressSolutions',
  activeEcommHosting = 'activeEcommHosting',
  activeYoastSEOPremium = 'activeYoastSEOPremium',
  purchHostingLast7Days = 'purchHostingLast7Days',
  purchEcommLast7Days = 'purchEcommLast7Days',
  activeProProducts = 'activeProProducts',
  activeYodleCustomers = 'activeYodleCustomers',
  activeWebsiteTrial = 'activeWebsiteTrial',
  activeBusinessDirectories = 'activeBusinessDirectories',
  activeDEP = 'activeDEP',
  activeBasicHosting = 'activeBasicHosting',
  activeChoicePlus = 'activeChoicePlus',
  activeProHosting = 'activeProHosting',
  activeDIFMHosting = 'activeDIFMHosting',
  activeProOnlineStore = 'activeProOnlineStore',
  activeProWebsite = 'activeProWebsite',
  activeBuilder = 'activeBuilder',
  activeTitanMail = 'activeTitanMail',
  activePlusHosting = 'activePlusHosting',
  activeWPCloud = 'activeWPCloud',
  activeBusinessEssential = 'activeBusinessEssential',
  activeOnlineMarketplace = 'activeOnlineMarketplace',
  activeOnlineStore = 'activeOnlineStore',
  activeEcommDash = 'activeEcommDash',
  activeWebsiteMarketing = 'activeWebsiteMarketing',
  activeBuilderSplus = 'activeBuilderSplus',
  activeHighPerformanceHosting = 'activeHighPerformanceHosting',
  activeEcommMySchedular = 'activeEcommMySchedular',
  activeEcommEssential = 'activeEcommEssential',
  activeEcommPremium = 'activeEcommPremium',
  activeEcommEssentialUnbundled = 'activeEcommEssentialUnbundled',
  activeEcommPremiumUnbundled = 'activeEcommPremiumUnbundled',
  activeWordpressHostingNetsol = 'activeWordpressHostingNetsol',
}

export const BUF_RND = 'NDEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIzNDU2Nzg5MDE=';

export enum skus {
  WEB_PPC_YODLE = 'WEB_PPC_YODLE',
  WEB_WEBSITE_YODLE = 'WEB_WEBSITE_YODLE',
  BH_PKG_WP_ENT = 'BH_PKG_WP_ENT',
  BH_PKG_WP_PLUS = 'BH_PKG_WP_PLUS',
  BH_PKG_WP_CHOICE_PLUS = 'BH_PKG_WP_CHOICE_PLUS',
  BH_PKG_WP_PRO = 'BH_PKG_WP_PRO',
  BH_HP_PKG_STARTER = 'BH_HP_PKG_STARTER',
  BH_HP_PKG_PLUS = 'BH_HP_PKG_PLUS',
  BH_HP_PKG_BUSINESS = 'BH_HP_PKG_BUSINESS',
  BH_HP_PKG_PRO = 'BH_HP_PKG_PRO',
  BH_SH_PKG_DESTINY_STARTER = 'BH_SH_PKG_DESTINY_STARTER',
  BH_SH_PKG_DESTINY_PLUS = 'BH_SH_PKG_DESTINY_PLUS',
  BH_SH_PKG_DESTINY_BUSINESS = 'BH_SH_PKG_DESTINY_BUSINESS',
  BH_SH_PKG_DESTINY_PRO = 'BH_SH_PKG_DESTINY_PRO',
}

// Each of these Ids are to be used only once in the entire codebase
export enum SFLOGGER_IDS {
  SFLOG_ID_01 = 'SFLOG_ID_01', // already used
  SFLOG_ID_02 = 'SFLOG_ID_02', // already used
  SFLOG_ID_03 = 'SFLOG_ID_03', // already used
  SFLOG_ID_04 = 'SFLOG_ID_04', // already used
  SFLOG_ID_05 = 'SFLOG_ID_05', // already used
  SFLOG_ID_06 = 'SFLOG_ID_06', // already used
  SFLOG_ID_07 = 'SFLOG_ID_07', // already used
  SFLOG_ID_08 = 'SFLOG_ID_08', // already used
  SFLOG_ID_09 = 'SFLOG_ID_09', // already used

  SFLOG_ID_10 = 'SFLOG_ID_10', // already used
  SFLOG_ID_11 = 'SFLOG_ID_11', // already used
  SFLOG_ID_12 = 'SFLOG_ID_12', // already used
  SFLOG_ID_13 = 'SFLOG_ID_13', // already used
  SFLOG_ID_14 = 'SFLOG_ID_14', // already used
  SFLOG_ID_15 = 'SFLOG_ID_15', // already used
  SFLOG_ID_16 = 'SFLOG_ID_16', // already used
  SFLOG_ID_17 = 'SFLOG_ID_17', // already used
  SFLOG_ID_18 = 'SFLOG_ID_18', // already used
  SFLOG_ID_19 = 'SFLOG_ID_19', // already used

  SFLOG_ID_20 = 'SFLOG_ID_20', // already used
  SFLOG_ID_21 = 'SFLOG_ID_21', // already used
  SFLOG_ID_22 = 'SFLOG_ID_22', // already used
  SFLOG_ID_23 = 'SFLOG_ID_23', // already used
  SFLOG_ID_24 = 'SFLOG_ID_24', // already used
  SFLOG_ID_25 = 'SFLOG_ID_25', // already used
  SFLOG_ID_26 = 'SFLOG_ID_26', // already used
  SFLOG_ID_27 = 'SFLOG_ID_27', // already used
  SFLOG_ID_28 = 'SFLOG_ID_28', // already used
  SFLOG_ID_29 = 'SFLOG_ID_29', // already used

  SFLOG_ID_30 = 'SFLOG_ID_30', // already used
  SFLOG_ID_31 = 'SFLOG_ID_31', // already used
  SFLOG_ID_32 = 'SFLOG_ID_32', // already used
  SFLOG_ID_33 = 'SFLOG_ID_33', // already used
  SFLOG_ID_34 = 'SFLOG_ID_34', // already used
  SFLOG_ID_35 = 'SFLOG_ID_35', // already used
  SFLOG_ID_36 = 'SFLOG_ID_36', // already used
  SFLOG_ID_37 = 'SFLOG_ID_37', // already used
  SFLOG_ID_38 = 'SFLOG_ID_38', // already used
  SFLOG_ID_39 = 'SFLOG_ID_39', // already used

  SFLOG_ID_40 = 'SFLOG_ID_40', // already used
  SFLOG_ID_41 = 'SFLOG_ID_41', // already used
  SFLOG_ID_42 = 'SFLOG_ID_42', // already used
  SFLOG_ID_43 = 'SFLOG_ID_43', // already used
  SFLOG_ID_44 = 'SFLOG_ID_44', // already used
  SFLOG_ID_45 = 'SFLOG_ID_45', // already used
  SFLOG_ID_46 = 'SFLOG_ID_46', // already used
  SFLOG_ID_47 = 'SFLOG_ID_47', // already used
  SFLOG_ID_48 = 'SFLOG_ID_48', // already used
  SFLOG_ID_49 = 'SFLOG_ID_49', // already used
  SFLOG_ID_50 = 'SFLOG_ID_50', // already used
  SFLOG_ID_51 = 'SFLOG_ID_51', // already used
  SFLOG_ID_52 = 'SFLOG_ID_52', // already used
  SFLOG_ID_53 = 'SFLOG_ID_53', // already used
  SFLOG_ID_54 = 'SFLOG_ID_54',
  SFLOG_ID_55 = 'SFLOG_ID_55',
  SFLOG_ID_56 = 'SFLOG_ID_56',
  SFLOG_ID_57 = 'SFLOG_ID_57',
  SFLOG_ID_58 = 'SFLOG_ID_58',
  SFLOG_ID_59 = 'SFLOG_ID_59',

  SFLOG_ID_60 = 'SFLOG_ID_60',
  SFLOG_ID_61 = 'SFLOG_ID_61',
  SFLOG_ID_62 = 'SFLOG_ID_62',
  SFLOG_ID_63 = 'SFLOG_ID_63',
  SFLOG_ID_64 = 'SFLOG_ID_64',
  SFLOG_ID_65 = 'SFLOG_ID_65',
  SFLOG_ID_66 = 'SFLOG_ID_66',
  SFLOG_ID_67 = 'SFLOG_ID_67',
  SFLOG_ID_68 = 'SFLOG_ID_68',
  SFLOG_ID_69 = 'SFLOG_ID_69',
}
