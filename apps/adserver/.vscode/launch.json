{
    "version": "0.2.0",
    "configurations": [        
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program (nodemon with env)",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "runtimeExecutable": "nodemon", 
            "runtimeArgs": [ 
                "--exec",
                "ts-node",
                "-r",
                "tsconfig-paths/register",
                "server.ts"
            ],
            "env": {  
                "SWAGGER": "true"
            },
        },
        {
            "name": "Debug Jest Tests",
            "type": "node",
            "request": "launch",
            "runtimeArgs": [
              "--inspect-brk",
              "${workspaceRoot}/node_modules/jest/bin/jest.js",
              "--forceExit",
              "--verbose"
            ],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen"
          }        
    ]
}



