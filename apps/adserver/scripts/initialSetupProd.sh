if ! command -v jq &> /dev/null
then
    echo "jq could not be found"
    exit 1
fi
app_name=$(cat package.json | jq '.org' | sed 's/"//g')

echo $app_name

oc new-build -n adserver-prod --strategy=docker --name=$app_name --binary
oc start-build $app_name -n adserver-prod --from-dir=. --follow
oc new-app $app_name -n adserver-prod
oc expose svc/$app_name -n adserver-prod
oc expose deployment/$app_name -n adserver-prod --port=8080 --target-port=8080 --name=$app_name
oc get pods