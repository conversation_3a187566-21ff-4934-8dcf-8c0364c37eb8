import type { Config } from 'jest';

const config: Config = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    moduleFileExtensions: ['ts', 'js'], 
    transform: {
        '^.+\\.ts$': 'ts-jest',
    },
    testMatch: [
        "<rootDir>/app/**/*.test.ts" 
    ],
    collectCoverage: true,
    collectCoverageFrom: ['src/**/*.ts'],
    moduleNameMapper: {
        '^app/(.*)$': '<rootDir>/app/$1',
    },
    watch: false,
    testTimeout: 15000
};

export default config;