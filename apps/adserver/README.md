## nfd-adserver

## Dev Notes

- We build the entire Ts project to a single bundle as we might want to deploy this bundle to Lambda or something. Better to have options
- Because of our build process, we can just do `node ./dist/server.js` either locally or inside Docker and it'll work

## Requirements

- Node 20 LTS
- Postman if you want to view the collection (recommended)
- `jq` (brew install jq)

## How to deploy to QA
- make sure you're added to the `adserverv2` project on the atlanta1 OKD instance
- make sure you have the OKD command line tools installed
- make sure you're logged in
- run `bash ./scripts/deployToOkd.sh`


## Stuff to do
1. update the changelog
2. fix the TODOs lol
3. extract logic into services