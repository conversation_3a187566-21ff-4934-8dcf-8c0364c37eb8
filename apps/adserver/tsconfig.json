{
  "compilerOptions": {
    "target": "esnext",
    "module": "commonjs",
    "alwaysStrict": true,
    "noImplicitThis": true,
    "strictPropertyInitialization": true,
    "strictFunctionTypes": true,
    "noImplicitAny": true,
    "allowJs": true,
    "checkJs": false,
    "outDir": "dist",
    "rootDir": "../../",  // Move this up to include the entire monorepo
    "strict": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "strictNullChecks": true,
    "resolveJsonModule": true,
    "sourceMap": true,
    "baseUrl": ".",
    "moduleResolution": "node",
    "paths": {
      "@repo/common": ["../../packages/common/src"]
    }
  },
  "exclude": [
    "node_modules",
    "dist",
  ],
  "include": [
    "./app",
    ".",
  ]
}