import { defineConfig } from "tsup";
const pkg = require('./package.json');
const dependencies = Object.keys(pkg.dependencies || {});
const devDependencies = Object.keys(pkg.devDependencies || {});
const allDependencies = [...dependencies, ...devDependencies];
// export default defineConfig({
//   entry: ["server.ts"], // Your entry file
//   format: ["cjs"], // Output CommonJS
//   bundle: true, // Bundle all dependencies
//   minify: true, // Minify the output
//   platform: "node", // Target Node.js
//   target: "node20", // Ensure compatibility with Node 20
//   clean: true, // Clean `dist/` before building
//   sourcemap: false, // Disable sourcemaps (optional)
//   dts: false, // Disable TypeScript declaration files (optional)
//   treeshake: true, // Remove unused code
//   noExternal: ["*"], // Ensures all dependencies are bundled
// });

export default defineConfig({
  entry: ["./server.ts"],
  format: ["cjs"],
  outDir: "./dist",
  outExtension: () => ({ js: '.js' }),
  target: "node20",
  platform: "node",
  minify: true,
  clean: true,
  dts: false,
  external: [], // This is key - ensures all dependencies are bundled
  noExternal: allDependencies,
  bundle: true, // Explicitly enable bundling
});