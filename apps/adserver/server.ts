import app from "./app";
import cluster from "cluster";
import os from "os";
import { version } from "./package.json";

const allowClustering = process.env.ALLOW_CLUSTERING === 'true';

// Horizontal scaling for kubernetes
// If the current is master, create the fork for all the CPUs
if (cluster.isMaster && allowClustering) {
  for (let i = 0; i < os.cpus().length; i++) {
    cluster.fork();
  }
  cluster.on("exit", (worker) => {
    console.error(`Worker has exit ${worker.process.pid}, creating another...`);
    cluster.fork();
  });
} else {
  const port = process.env.PORT || process.env.OPENSHIFT_NODEJS_PORT || 8080;
  const ip = process.env.IP || process.env.OPENSHIFT_NODEJS_IP || "0.0.0.0";

  console.log(`Ready service version ${version} on ${ip}:${port}`);
  app.listen(port);
}
