const getSwaggerDocs = (surface: string) => {
  const swaggerJson = {
    openapi: '3.0.3',
    servers: [
      {
        url: 'https://global-nfd-adserver.apps.atlanta1.newfoldmb.com/',
        description: 'QA server',
      },
      {
        url: 'https://adam.newfold.com/',
        description: 'Prod server',
      },
      {
        url: 'http://localhost:8080/',
        description: 'Local Server',
      },
    ],
    info: {
      version: '3.0.3',
      title: 'Newfold Ad Server',
      description:
        '#### Ad Server for Cross Sell and Experience Fragments\nFeel free to make a path or an operation and use **Try It Out** to test it. The ad server will send back the response.\n',
    },
    components: getSwaggerComponents(surface),
    paths: getSwaggerPaths(surface),
  };
  return swaggerJson;
};

const getSwaggerComponents = (surface: string) => {
  const components: any = {
    parameters: {},
    schemas: {},
  };
  if (surface === 'expFrag') {
    components.schemas = {
      ExpFragRequest: {
        type: 'object',
        properties: {
          brand: {
            type: 'string',
            example: 'HOSTGATOR or BLUEHOST or NETWORKSOLUTIONS etc',
          },
          env: {
            type: 'string',
            example: 'qa or prod or stg or development',
          },
          responseType: {
            type: 'string',
            example: 'frag',
          },
          expFrag: {
            type: 'string or array',
            example:
              "/content/experience-fragments/netsol/site-header or ['/content/experience-fragments/netsol/site-header', '/content/experience-fragments/netsol/site-footer']",
          },
        },
      },
      ExpFragResponse: {
        type: 'object',
        properties: {
          status: {
            type: 'string',
            example: 'success',
          },
          statusDetails: {
            type: 'string',
            example: 'some optional technical detail',
          },
          response: {
            type: 'object',
            properties: {
              headContent: {
                type: 'string',
              },
              bodyContent: {
                type: 'string',
              },
              htmlString: {
                type: 'string',
              },
              linkTags: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    rel: {
                      type: 'string',
                      example: 'stylesheet',
                    },
                    href: {
                      type: 'string',
                      example:
                        'https://www.bluehost.com/etc.clientlibs/hostgator/clientlibs/clientlib-site.min.b7780d23802ef0a4c44904aaab43fb8b.css',
                    },
                    type: {
                      type: 'string',
                      example: 'text/css',
                    },
                  },
                },
              },
              scriptTags: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    src: {
                      type: 'string',
                      example:
                        'https://www.bluehost.com/etc.clientlibs/clientlibs/granite/jquery.min.cee8557e8779d371fe722bbcdd3b3eb7.js',
                    },
                  },
                },
              },
            },
          },
        },
      },
    };
  } else {
    components.schemas = {
      XSellRequest: {
        type: 'object',
        properties: {
          containerName: {
            type: 'string',
            example: 'InCart or AMHPCards or AMWI',
          },
          channel: {
            type: 'string',
            example: 'Web or Mobile or Tablet',
          },
          brand: {
            type: 'string',
            example: 'HOSTGATOR or BLUEHOST or NETWORKSOLUTIONS etc',
          },
          responseType: {
            type: 'string',
            example: 'frag',
          },
          env: {
            type: 'string',
            example: 'qa or prod or stg or development',
          },
          isLoggedIn: {
            type: 'boolean',
            example: 'true or false',
          },
          ...(surface !== 'cart'
            ? {
                userId: {
                  type: 'string',
                  example: 'pass in personOrgId, example *********',
                },
              }
            : {
                accountId: {
                  type: 'string',
                  example: 'pass in accountId, example *********',
                },
              }),
          isLargeUser: {
            type: 'boolean',
            example: 'true or false',
          },
          countryCode: {
            type: 'string',
            example: 'US or CA',
          },
          isAffiliate: {
            type: 'boolean',
            example: 'true or false',
          },
          testOffers: {
            type: 'array',
            items: {
              type: 'string',
            },
            example: "['ACTIVE_DOMAIN_EXCEEDS_PRREG']",
            description: 'To force some offers, we can pass this array',
          },
          ...(surface === 'cart' && {
            cart: {
              type: 'object',
              example: {
                cards: [
                  {
                    products: [
                      {
                        productCode: 'BH_PKG_WP_ENT',
                      },
                    ],
                  },
                ],
                currencyCode: {
                  type: 'string',
                  example: 'USD or GBP or AUD',
                },
              },
            },
          }),
          ...((surface === 'amhp' || surface === 'amwi') && {
            currencyCode: {
              type: 'string',
              example: 'USD or GBP or AUD',
            },
          }),
        },
        required: ['containerName', 'brand', 'cart'],
      },
      XSellResponse: {
        type: 'object',
        properties: {
          status: {
            type: 'string',
            example: 'success',
          },
          response: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                adDetails: {
                  type: 'object',
                  properties: {
                    productSku: {
                      type: 'string',
                      example: 'CODEGUARD_BASIC_V2',
                    },
                    productMarkup: {
                      type: 'object',
                      properties: {
                        headContent: {
                          type: 'string',
                        },
                        bodyContent: {
                          type: 'string',
                        },
                        htmlString: {
                          type: 'string',
                        },
                        linkTags: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              rel: {
                                type: 'string',
                                example: 'stylesheet',
                              },
                              href: {
                                type: 'string',
                                example:
                                  'https://www.bluehost.com/etc.clientlibs/hostgator/clientlibs/clientlib-site.min.b7780d23802ef0a4c44904aaab43fb8b.css',
                              },
                              type: {
                                type: 'string',
                                example: 'text/css',
                              },
                            },
                          },
                        },
                        scriptTags: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              src: {
                                type: 'string',
                                example:
                                  'https://www.bluehost.com/etc.clientlibs/clientlibs/granite/jquery.min.cee8557e8779d371fe722bbcdd3b3eb7.js',
                              },
                            },
                          },
                        },
                      },
                    },
                    priority: {
                      type: 'number',
                      example: 1,
                    },
                    actionType: {
                      type: 'string',
                      example: 'addToCart or CTB or redirect',
                    },
                    pricingTerm: {
                      type: 'string',
                      example: 'YEAR',
                    },
                    landingCode: {
                      type: 'string',
                      example: 'P118C100S1N0B200A151D277E0000V100',
                    },
                    channelID: {
                      type: 'string',
                      example: '110',
                    },
                    coupon: {
                      type: 'string',
                      example: '50offCodeguard',
                    },
                    packageEligible: {
                      type: 'boolean',
                      example: 'true or false',
                    },
                    packageName: {
                      type: 'string',
                      example: 'businessEssentialsPPC or essentialHosting',
                    },
                    redirect: {
                      type: 'string',
                      example: 'https://www.bluehost.com/wordpress/wordpress-hosting',
                    },
                  },
                },
              },
            },
          },
        },
      },
    };
  }
  return components;
};
const getSwaggerPaths = (surface: string) => {
  const paths: any = {};
  if (surface === 'expFrag') {
    paths['/api/v1/getExpFrag'] = {
      post: {
        description: 'API for fetching AEM Experience Fragments',
        requestBody: {
          description:
            'The request body contains the context to fetch the experience fragment. Brand is a name of the brand. Response type is a expectation of the format of response. expFrag contains path as string or array of strings of experience fragments in the AEM.',
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ExpFragRequest',
              },
            },
          },
        },
        responses: {
          '200': {
            description:
              'The response contains the AEM fragment object or array of objects that contains HTML to display the fragment.',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ExpFragResponse',
                },
              },
            },
          },
          '404': {
            description: 'invalid brand or invalid env or URL param responseType',
          },
        },
      },
    };
  } else {
    paths['/api/v1/getXSell'] = {
      post: {
        description: 'API for fetching XSell data for products in a cart',
        requestBody: {
          description:
            'The request body contains the context to fetch the ad along with the expected response type. ContainerName is a name of the environment where the items are added. Channel is a platform or context where the XSell recommendation is being requested from such as website or mobile app. Brand is a name of the brand. Response type is a expectation of the format of response. Cart contains array of name of the items in the cart.',
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/XSellRequest',
              },
            },
          },
        },
        responses: {
          '200': {
            description:
              'The response contains the array of container. For each container there will be an array of ranked results. Each result contains channel name, AEM fragment that contains HTML to display the ad and AEM content variation json.',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/XSellResponse',
                },
              },
            },
          },
          '404': {
            description: 'containerName not found, invalid brand or invalid URL param responseType',
          },
        },
      },
    };
  }
  return paths;
};

export default getSwaggerDocs;
