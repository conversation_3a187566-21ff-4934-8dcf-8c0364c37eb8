{"name": "global-nfd-adserver", "version": "2.1.0", "description": "Ad Server", "org": "global-nfd", "main": "server.js", "scripts": {"start": "npm run build && node ./dist/server.js", "dev": "SWAGGER=true LOCAL_LOGS=true nodemon --exec ts-node -r tsconfig-paths/register server.ts", "devWindows": "SET SWAGGER=true && nodemon --exec ts-node -r tsconfig-paths/register server.ts", "startLocal": "node ./dist/server.js", "test": "jest --forceExit --cache --coverage=false", "build": "tsup", "deployToQa": "npm run build && bash ./scripts/deployToOkd.sh", "setupQa": "npm run build && bash ./scripts/initialSetup.sh", "setupProd": "npm run build && bash ./scripts/initialSetupProd.sh", "deployToProd": "npm run build && bash ./scripts/deployToOkdProd.sh", "drizzle:generate": "npx drizzle-kit generate", "drizzle:migrate": "npx drizzle-kit migrate", "drizzle:studio": "pnpm drizzle-kit studio --port 3001"}, "dependencies": {"axios": "^1.6.7", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.7", "drizzle-orm": "^0.41.0", "express": "^4.13.4", "pg": "^8.14.1", "swagger-ui-express": "^5.0.1", "typescript": "^5.6.3", "xss": "^1.0.15"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/cheerio": "^0.22.35", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.1", "@types/pg": "^8.11.11", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.7", "debug": "^4.4.0", "drizzle-kit": "^0.30.6", "jest": "^29.7.0", "nodemon": "^3.1.7", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsup": "^8.3.5", "tsx": "^4.19.3"}, "repository": {"type": "git", "url": "https://stash.newfold.com/projects/SAM/repos/sfbff-get-product-price"}, "license": "ISC"}