{"name": "@repo/common", "version": "0.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src/", "typecheck": "tsc --noEmit", "test": "jest"}, "jest": {"preset": "@repo/jest-presets/node"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/pg": "^8.11.11", "drizzle-kit": "^0.30.6", "jest": "^29.7.0", "tsup": "^8.0.2", "tsx": "^4.19.3", "typescript": "^5.3.3"}, "dependencies": {"arktype": "2.0.0-rc.4", "dotenv": "^16.4.7", "drizzle-orm": "^0.41.0", "pg": "^8.14.1"}}