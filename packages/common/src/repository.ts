import { eq, SQL } from 'drizzle-orm';
import { db } from './db';
import {
  decisionsTable,
  NewDecision,
  NewRule,
  rulesTable,
  UpdateDecision,
  UpdateRule,
} from './schemas';

// export async function createDecision(newDecision: typeof decisionsTable.$inferInsert) {
//     try {
//         await db.insert(decisionsTable).values(newDecision);
//         console.log(`successfully inserted role ${newDecision.decisionId}`);
//     }
//     catch (e) {

//     }
// }

// export async function createRule(newRule: typeof rulesTable.$inferInsert) {
//     try {
//         await db.insert(rulesTable).values(newRule);
//         console.log(`successfully inserted rule ${newRule.id}`);
//     }
//     catch (e) {

//     }
// }

// export async function updateDecision(
//     id: typeof decisionsTable.decisionId,
//     newDecision: Partial<typeof decisionsTable.$inferInsert>
// ) {
//     try {
//         await db.update(decisionsTable)
//             .set(newDecision)
//             .where(eq(decisionsTable.decisionId, id));
//     }
//     catch (e) { }
// }

// export async function updateDecision(
//     id: typeof decisionsTable.decisionId,
//     newDecision: Partial<typeof decisionsTable.$inferInsert>
// ) {
//     try {
//         await db.update(decisionsTable)
//             .set(newDecision)
//             .where(eq(decisionsTable.decisionId, id));
//     }
//     catch (e) { }
// }

/**
 * Creates a new rule in the db. handle your own exceptions
 * @param data
 * @returns
 */
export async function createRule(data: NewRule) {
  try {
    const result = await db!.insert(rulesTable).values(data).returning();
    return result[0];
  } catch (error: any) {
    console.error('Error creating rule:', error);
    throw new Error(
      `Creating rule:createRule() - could not create rule in db: ${error.message}`
    );
  }
}

/**
 * Handle your own exceptions
 * @param id
 * @param data
 * @returns
 */
export async function updateRule(id: number, data: UpdateRule) {
  try {
    const result = await db!
      .update(rulesTable)
      .set(data)
      .where(eq(rulesTable.id, id))
      .returning();
    return result[0];
  } catch (error: any) {
    console.error('Error updating rule:', error);
    throw new Error(
      `Updating rule:updateRule() - could not update rule in db: ${error.message}`
    );
  }
}

/**
 * Handle your own exceptions
 * @param data
 * @returns
 */
export async function createDecision(data: NewDecision) {
  try {
    const result = await db!.insert(decisionsTable).values(data).returning();
    return result[0];
  } catch (error: any) {
    console.error('Error creating decision:', error);
    throw new Error(
      `Creating decision:createDecision() - could not create decision in db: ${error.message}`
    );
  }
}

interface SelectedRow {
  adIdentifier: string;
}

/**
 * Handle your own exceptions
 * @param where
 * @param select
 * @returns
 */
export async function readDecision(
  where?: SQL,
  select?: any
): Promise<SelectedRow[]> {
  try {
    const result: SelectedRow[] = (await db!
      .select(select)
      .from(decisionsTable)
      .where(where)) as SelectedRow[];
    return result;
  } catch (error: any) {
    console.error('Error reading decision:', error);
    throw new Error(
      `Reading decision:readDecision() - could not read decision from db: ${error.message}`
    );
  }
}

/**
 * Handle your own exceptions
 * @param id
 * @param data
 * @returns
 */
export async function updateDecision(id: number, data: UpdateDecision) {
  try {
    const result = await db!
      .update(decisionsTable)
      .set(data)
      .where(eq(decisionsTable.decisionId, id))
      .returning();
    return result[0];
  } catch (error: any) {
    console.error('Error updating decision:', error);
    throw new Error(
      `Updating decision:updateDecision() - could not update decision in db: ${error.message}`
    );
  }
}
