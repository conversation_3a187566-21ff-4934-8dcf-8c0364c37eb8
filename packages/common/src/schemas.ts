/**
 * Yes, we want to use snake_case for the table names as
 * PG likes snake case and defaults to that when doing
 * complex stuff like relations or multi joins
 *
 * But since we use camelCase in our project, we have to
 * explicitly define both - camelCase in the Js project
 * and snake_case for the SQL columns
 */

import { sql, InferInsertModel } from "drizzle-orm";
import {
  integer,
  text,
  pgTable,
  varchar,
  timestamp,
  boolean,
  numeric,
  serial,
  bigint,
} from "drizzle-orm/pg-core";

export const rulesTable = pgTable("rules", {
  id: serial("id").primaryKey(),
  brand: varchar("brand", { length: 255 }).notNull(),
  url: varchar("url", { length: 255 }).notNull(),
  containerName: varchar("container_name", { length: 255 }).notNull(),
  actionType: varchar("action_type", { length: 255 }).notNull(),
  cartIncludesAll: text("cart_includes_all")
    .array()
    .notNull()
    .default(sql`'{}'::text[]`),
  cartShouldNotInclude: text("cart_should_not_include")
    .array()
    .notNull()
    .default(sql`'{}'::text[]`),
  pricingTerm: varchar("pricing_term", { length: 255 }).notNull(),
  priceDisplayTerm: varchar("price_display_term", { length: 255 }).notNull(),
  landingCode: varchar("landing_code", { length: 255 }).notNull(),
  channelId: varchar("channel_id", { length: 255 }).notNull(),
  coupon: text("coupon"),
  priority: integer("priority"),
  fragmentAdSku: text("fragment_ad_sku"),
  fragmentHtmlPath: text("fragment_html_path"),
  fragmentHtmlPathQa: text("fragment_html_path_qa"),
});

export const decisionsTable = pgTable("decisions", {
  decisionId: serial("decision_id").primaryKey(),
  brand: varchar("brand", { length: 32 }).notNull(),
  ruleId: integer("rule_id").references(() => rulesTable.id),
  decisionDate: timestamp("decision_date", { withTimezone: true })
    .notNull()
    .defaultNow(),
  customerId: bigint("customer_id", { mode: "number" }),
  accountId: bigint("account_id", { mode: "number" }),
  surface: varchar("surface", { length: 255 }).notNull(),
  adIdentifier: varchar("ad_identifier", { length: 255 }).notNull(),
});
export type NewRule = InferInsertModel<typeof rulesTable>;
export type UpdateRule = Partial<NewRule>;

export type NewDecision = InferInsertModel<typeof decisionsTable>;
export type UpdateDecision = Partial<NewDecision>;
