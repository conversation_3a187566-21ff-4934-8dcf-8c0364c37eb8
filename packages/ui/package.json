{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./*": "./src/*.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^2.4.4", "@types/node": "^22.13.10", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "eslint": "^9.23.0", "typescript": "5.8.2"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}